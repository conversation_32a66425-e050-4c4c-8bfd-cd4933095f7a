"""Layered-window overlay for drawing YOLO boxes於 Valorant 螢幕上.

改用 PyQt6 生成一個*全螢幕透明、點擊穿透*的視窗，比直接在 Desktop DC 畫圖穩定，
不易被全螢幕遊戲覆蓋，也較不會被 Vanguard 擋掉。
"""

from multiprocessing import shared_memory
import sys
import numpy as np


def run_overlay_process(box_shm_name: str) -> None:  # noqa: D401
    """子行程入口：顯示疊層視窗並持續繪製 Box。"""

    # 避免 Qt 與主程式 DPI awareness 衝突
    import os

    os.environ["QT_ENABLE_HIGHDPI_SCALING"] = "1"
    os.environ["QT_SCALE_FACTOR"] = "1"

    from PyQt6 import QtWidgets, QtGui, QtCore  # 避免主程式沒用到時浪費 import 時間

    try:
        shm = shared_memory.SharedMemory(name=box_shm_name)
    except FileNotFoundError:
        # 共享內存不存在，直接結束
        return

    box_shape = (20, 6)
    box_dtype = np.float32
    box_array = np.ndarray(box_shape, dtype=box_dtype, buffer=shm.buf)

    # 與截圖區域保持一致
    CAPTURE_W, CAPTURE_H = 200, 200

    # 取得螢幕解析度
    screen = QtWidgets.QApplication.primaryScreen() if QtWidgets.QApplication.instance() else None

    def screen_center() -> QtCore.QPoint:
        scr = screen or QtWidgets.QApplication.primaryScreen()
        geo = scr.geometry()
        return QtCore.QPoint(int(geo.width() / 2), int(geo.height() / 2))

    class OverlayWidget(QtWidgets.QWidget):
        def __init__(self):
            super().__init__(None, QtCore.Qt.WindowType.FramelessWindowHint |
                                   QtCore.Qt.WindowType.WindowStaysOnTopHint |
                                   QtCore.Qt.WindowType.Tool)

            self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TranslucentBackground)
            self.setAttribute(QtCore.Qt.WidgetAttribute.WA_TransparentForMouseEvents)

            # 全螢幕覆蓋
            scr_geo = QtWidgets.QApplication.primaryScreen().geometry()
            self.setGeometry(scr_geo)

            # 定時刷新 (約 60 FPS)
            self.timer = QtCore.QTimer(self)
            self.timer.timeout.connect(self.update)  # 觸發 paintEvent
            self.timer.start(int(1000 / 60))

        def _read_boxes(self) -> list[tuple[int, int, int, int]]:
            data_snapshot = np.copy(box_array)
            ctr = screen_center()
            cap_left = ctr.x() - CAPTURE_W // 2
            cap_top = ctr.y() - CAPTURE_H // 2

            boxes: list[tuple[int, int, int, int]] = []
            for row in data_snapshot:
                x1, y1, x2, y2 = row[:4]
                if not (x1 or y1 or x2 or y2):
                    continue
                scr_x1 = int(x1 + cap_left)
                scr_y1 = int(y1 + cap_top)
                scr_x2 = int(x2 + cap_left)
                scr_y2 = int(y2 + cap_top)
                boxes.append((scr_x1, scr_y1, scr_x2, scr_y2))
            return boxes

        def paintEvent(self, event):  # noqa: D401,N802
            painter = QtGui.QPainter(self)
            painter.setRenderHint(QtGui.QPainter.RenderHint.Antialiasing)

            pen = QtGui.QPen(QtGui.QColor(255, 0, 0))
            pen.setWidth(2)
            painter.setPen(pen)

            for x1, y1, x2, y2 in self._read_boxes():
                painter.drawRect(x1, y1, x2 - x1, y2 - y1)

            painter.end()

    app = QtWidgets.QApplication(sys.argv)
    ow = OverlayWidget()
    ow.showFullScreen()

    try:
        sys.exit(app.exec())
    finally:
        try:
            shm.close()
        except Exception:
            pass 