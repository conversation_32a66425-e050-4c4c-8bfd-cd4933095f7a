<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1693</width>
    <height>615</height>
   </rect>
  </property>
  <property name="cursor">
   <cursorShape>ArrowCursor</cursorShape>
  </property>
  <property name="acceptDrops">
   <bool>false</bool>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="whatsThis">
   <string>简化窗口</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <property name="animated">
   <bool>false</bool>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <widget class="QLabel" name="show_video">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>10</y>
      <width>511</width>
      <height>511</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>Microsoft YaHei</family>
     </font>
    </property>
    <property name="cursor">
     <cursorShape>ArrowCursor</cursorShape>
    </property>
    <property name="styleSheet">
     <string notr="true">

QFrame {
	background-color: rgb(63, 63, 63);
    border-radius: 20px;
    /* overflow: hidden;  确保内容不会溢出圆角边界 */
}</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::Shape::Box</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Shadow::Raised</enum>
    </property>
    <property name="lineWidth">
     <number>3</number>
    </property>
    <property name="text">
     <string>预览已关闭</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignmentFlag::AlignCenter</set>
    </property>
   </widget>
   <widget class="QPushButton" name="OpVideoButton">
    <property name="geometry">
     <rect>
      <x>540</x>
      <y>530</y>
      <width>141</width>
      <height>51</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>Microsoft YaHei</family>
      <pointsize>12</pointsize>
     </font>
    </property>
    <property name="cursor">
     <cursorShape>PointingHandCursor</cursorShape>
    </property>
    <property name="text">
     <string>打开视频预览</string>
    </property>
   </widget>
   <widget class="QPushButton" name="OpYoloButton">
    <property name="geometry">
     <rect>
      <x>690</x>
      <y>530</y>
      <width>141</width>
      <height>51</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>Microsoft YaHei</family>
      <pointsize>12</pointsize>
     </font>
    </property>
    <property name="cursor">
     <cursorShape>PointingHandCursor</cursorShape>
    </property>
    <property name="text">
     <string>开启 YOLO</string>
    </property>
   </widget>
   <widget class="QPushButton" name="settingsYoloButton">
    <property name="geometry">
     <rect>
      <x>840</x>
      <y>530</y>
      <width>51</width>
      <height>51</height>
     </rect>
    </property>
    <property name="cursor">
     <cursorShape>PointingHandCursor</cursorShape>
    </property>
    <property name="whatsThis">
     <string>设置面板</string>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="icon">
     <iconset>
      <normaloff>ico/gear-modified.png</normaloff>ico/gear-modified.png</iconset>
    </property>
    <property name="iconSize">
     <size>
      <width>32</width>
      <height>32</height>
     </size>
    </property>
    <property name="autoRepeatDelay">
     <number>300</number>
    </property>
   </widget>
   <widget class="QWidget" name="settingsPanel" native="true">
    <property name="geometry">
     <rect>
      <x>1320</x>
      <y>0</y>
      <width>381</width>
      <height>621</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>Microsoft YaHei</family>
      <pointsize>12</pointsize>
     </font>
    </property>
    <property name="contextMenuPolicy">
     <enum>Qt::ContextMenuPolicy::NoContextMenu</enum>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(70, 70, 70);</string>
    </property>
    <widget class="QLabel" name="label">
     <property name="geometry">
      <rect>
       <x>100</x>
       <y>30</y>
       <width>171</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>微软雅黑</family>
       <pointsize>18</pointsize>
      </font>
     </property>
     <property name="text">
      <string>模型设置面板</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_7">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>320</y>
       <width>101</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="text">
      <string>YOLO置信度:</string>
     </property>
    </widget>
    <widget class="QSlider" name="confSlider">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>320</y>
       <width>160</width>
       <height>18</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei UI</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="maximum">
      <number>100</number>
     </property>
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
    </widget>
    <widget class="QLCDNumber" name="confNumber">
     <property name="geometry">
      <rect>
       <x>290</x>
       <y>320</y>
       <width>61</width>
       <height>23</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei UI</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::Box</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>1</number>
     </property>
     <property name="digitCount">
      <number>5</number>
     </property>
     <property name="segmentStyle">
      <enum>QLCDNumber::SegmentStyle::Flat</enum>
     </property>
    </widget>
    <widget class="QPushButton" name="closeYoloSettingsButton">
     <property name="geometry">
      <rect>
       <x>320</x>
       <y>10</y>
       <width>41</width>
       <height>41</height>
      </rect>
     </property>
     <property name="cursor">
      <cursorShape>PointingHandCursor</cursorShape>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(0, 0, 0);</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset>
       <normaloff>ico/close-modified.png</normaloff>ico/close-modified.png</iconset>
     </property>
     <property name="iconSize">
      <size>
       <width>28</width>
       <height>28</height>
      </size>
     </property>
    </widget>
    <widget class="QLabel" name="label_9">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>360</y>
       <width>91</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="text">
      <string>YOLO锁帧:</string>
     </property>
    </widget>
    <widget class="QSpinBox" name="yoloFpsSetSpinBox">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>360</y>
       <width>101</width>
       <height>31</height>
      </rect>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_10">
     <property name="enabled">
      <bool>false</bool>
     </property>
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>365</y>
       <width>31</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei UI</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="text">
      <string>FPS</string>
     </property>
    </widget>
    <widget class="QPushButton" name="RestartButton">
     <property name="geometry">
      <rect>
       <x>60</x>
       <y>540</y>
       <width>111</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei UI</family>
       <pointsize>14</pointsize>
      </font>
     </property>
     <property name="text">
      <string>RESTART</string>
     </property>
    </widget>
    <widget class="QPushButton" name="saveButton">
     <property name="geometry">
      <rect>
       <x>210</x>
       <y>540</y>
       <width>111</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei UI</family>
       <pointsize>14</pointsize>
      </font>
     </property>
     <property name="text">
      <string>SAVE</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_11">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>280</y>
       <width>101</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="text">
      <string>YOLO模型:</string>
     </property>
    </widget>
    <widget class="QLabel" name="modelFileLabel">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>280</y>
       <width>161</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Cascadia Mono</family>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::Box</enum>
     </property>
     <property name="text">
      <string>暂未选择文件</string>
     </property>
    </widget>
    <widget class="QPushButton" name="chooseModelButton">
     <property name="geometry">
      <rect>
       <x>290</x>
       <y>275</y>
       <width>61</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="text">
      <string>选择</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_17">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>230</y>
       <width>371</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>微软雅黑</family>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string>———————————YOLO设置———————————</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="label_18">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>400</y>
       <width>101</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="text">
      <string>推理进程模式:</string>
     </property>
    </widget>
    <widget class="QComboBox" name="ProcessModeComboBox">
     <property name="geometry">
      <rect>
       <x>120</x>
       <y>401</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <item>
      <property name="text">
       <string>单进程模式</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>多进程模式</string>
      </property>
     </item>
    </widget>
    <widget class="QLabel" name="label_19">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>70</y>
       <width>371</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>微软雅黑</family>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="text">
      <string>———————————软件设置———————————</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
    <widget class="QCheckBox" name="topWindowCheckBox">
     <property name="geometry">
      <rect>
       <x>15</x>
       <y>110</y>
       <width>91</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="text">
      <string>窗口置顶</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_8">
     <property name="geometry">
      <rect>
       <x>240</x>
       <y>407</y>
       <width>21</width>
       <height>21</height>
      </rect>
     </property>
     <property name="sizePolicy">
      <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="toolTip">
      <string>保存后重启生效</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="pixmap">
      <pixmap>ico/warningtriangle_87535-modified.png</pixmap>
     </property>
     <property name="scaledContents">
      <bool>true</bool>
     </property>
    </widget>
    <widget class="QPushButton" name="reloadModelButton">
     <property name="geometry">
      <rect>
       <x>350</x>
       <y>275</y>
       <width>31</width>
       <height>31</height>
      </rect>
     </property>
     <property name="toolTip">
      <string>立即重新加载模型</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset>
       <normaloff>ico/restartr-modified.png</normaloff>ico/restartr-modified.png</iconset>
     </property>
    </widget>
    <widget class="QCheckBox" name="topWindowCheckBox_2">
     <property name="enabled">
      <bool>false</bool>
     </property>
     <property name="geometry">
      <rect>
       <x>130</x>
       <y>110</y>
       <width>101</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="text">
      <string>随机窗口名</string>
     </property>
    </widget>
    <widget class="QCheckBox" name="checkBox_2">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="geometry">
      <rect>
       <x>290</x>
       <y>370</y>
       <width>78</width>
       <height>19</height>
      </rect>
     </property>
     <property name="text">
      <string>无上限</string>
     </property>
    </widget>
    <widget class="QCheckBox" name="unlockWindowSizeCheckBox">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="geometry">
      <rect>
       <x>15</x>
       <y>150</y>
       <width>121</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="text">
      <string>解锁窗口大小</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_28">
     <property name="geometry">
      <rect>
       <x>170</x>
       <y>150</y>
       <width>21</width>
       <height>21</height>
      </rect>
     </property>
     <property name="sizePolicy">
      <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="toolTip">
      <string>开发人员选项</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="pixmap">
      <pixmap>ico/warningtriangle_87535-modified.png</pixmap>
     </property>
     <property name="scaledContents">
      <bool>true</bool>
     </property>
    </widget>
    <widget class="QPushButton" name="resetSizeButton">
     <property name="geometry">
      <rect>
       <x>135</x>
       <y>145</y>
       <width>31</width>
       <height>31</height>
      </rect>
     </property>
     <property name="toolTip">
      <string>重置窗口大小</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset>
       <normaloff>ico/restartr-modified.png</normaloff>ico/restartr-modified.png</iconset>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="overlay" native="true">
    <property name="geometry">
     <rect>
      <x>1670</x>
      <y>630</y>
      <width>971</width>
      <height>611</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgba(56, 56, 56, 180);</string>
    </property>
   </widget>
   <widget class="QWidget" name="closeMessageWidget" native="true">
    <property name="geometry">
     <rect>
      <x>1280</x>
      <y>630</y>
      <width>241</width>
      <height>171</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(45, 45, 45);</string>
    </property>
    <widget class="QLabel" name="label_5">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>70</y>
       <width>111</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="text">
      <string>软件关闭中...</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="widget" native="true">
    <property name="geometry">
     <rect>
      <x>1530</x>
      <y>630</y>
      <width>131</width>
      <height>41</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 0, 0);</string>
    </property>
    <widget class="QLabel" name="floatingMessageLabel">
     <property name="geometry">
      <rect>
       <x>40</x>
       <y>10</y>
       <width>51</width>
       <height>21</height>
      </rect>
     </property>
     <property name="text">
      <string>ERROR</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
     <property name="wordWrap">
      <bool>false</bool>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="statusDisplayWidget" native="true">
    <property name="geometry">
     <rect>
      <x>1050</x>
      <y>630</y>
      <width>211</width>
      <height>41</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(255, 255, 255);
border-radius: 20px;      /* 圆形效果，半径设为 QLabel 宽高的一半 */</string>
    </property>
    <widget class="QLabel" name="statusDisplayLabel">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>5</y>
       <width>191</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>微软雅黑</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(0, 0, 0);</string>
     </property>
     <property name="text">
      <string>None</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
   </widget>
   <widget class="QWidget" name="settingWidget" native="true">
    <property name="geometry">
     <rect>
      <x>530</x>
      <y>10</y>
      <width>371</width>
      <height>511</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">
    border-radius: 20px;
    background-color: rgb(70, 70, 70);


</string>
    </property>
    <widget class="QFrame" name="itemFrame">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>371</width>
       <height>71</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgba(0, 0, 0, 0);
border-color: rgba(0, 0, 0, 0);</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::Box</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <property name="lineWidth">
      <number>0</number>
     </property>
     <widget class="QPushButton" name="pushButton_3">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>0</y>
        <width>31</width>
        <height>51</height>
       </rect>
      </property>
      <property name="cursor">
       <cursorShape>PointingHandCursor</cursorShape>
      </property>
      <property name="styleSheet">
       <string notr="true">
    border-radius: 20px;
background-color: rgba(0, 0, 0, 0);


</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="icon">
       <iconset>
        <normaloff>ico/back_turn.png</normaloff>ico/back_turn.png</iconset>
      </property>
      <property name="iconSize">
       <size>
        <width>28</width>
        <height>28</height>
       </size>
      </property>
     </widget>
     <widget class="QPushButton" name="pushButton_4">
      <property name="geometry">
       <rect>
        <x>330</x>
        <y>0</y>
        <width>31</width>
        <height>51</height>
       </rect>
      </property>
      <property name="cursor">
       <cursorShape>PointingHandCursor</cursorShape>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);
    border-radius: 20px;
</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="icon">
       <iconset>
        <normaloff>ico/back.png</normaloff>ico/back.png</iconset>
      </property>
      <property name="iconSize">
       <size>
        <width>28</width>
        <height>28</height>
       </size>
      </property>
     </widget>
     <widget class="QPushButton" name="basicSettingsPushButton">
      <property name="geometry">
       <rect>
        <x>150</x>
        <y>15</y>
        <width>75</width>
        <height>23</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>基础设置</string>
      </property>
     </widget>
     <widget class="QPushButton" name="softwareInformationPushButton">
      <property name="geometry">
       <rect>
        <x>240</x>
        <y>20</y>
        <width>75</width>
        <height>23</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>软件信息</string>
      </property>
     </widget>
     <widget class="QPushButton" name="advancedSettingsPushButton">
      <property name="geometry">
       <rect>
        <x>60</x>
        <y>20</y>
        <width>75</width>
        <height>23</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>高级设置</string>
      </property>
     </widget>
     <widget class="QLabel" name="redLine">
      <property name="geometry">
       <rect>
        <x>150</x>
        <y>40</y>
        <width>75</width>
        <height>3</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="pixmap">
       <pixmap>ico/RedLine.png</pixmap>
      </property>
      <property name="scaledContents">
       <bool>true</bool>
      </property>
     </widget>
    </widget>
   </widget>
   <widget class="QWidget" name="settingWidget_2" native="true">
    <property name="geometry">
     <rect>
      <x>910</x>
      <y>10</y>
      <width>371</width>
      <height>511</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">
    border-radius: 20px;
    background-color: rgb(70, 70, 70);


</string>
    </property>
   </widget>
   <widget class="QFrame" name="frame_3">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>910</x>
      <y>10</y>
      <width>371</width>
      <height>561</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true"/>
    </property>
    <property name="frameShape">
     <enum>QFrame::Shape::NoFrame</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Shadow::Plain</enum>
    </property>
    <widget class="QLabel" name="label_15">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>20</y>
       <width>91</width>
       <height>16</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
      </font>
     </property>
     <property name="text">
      <string>进程0: UI主进程</string>
     </property>
    </widget>
    <widget class="QTextEdit" name="log_output_03">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>400</y>
       <width>331</width>
       <height>91</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
    </widget>
    <widget class="QTextEdit" name="log_output_02">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>280</y>
       <width>331</width>
       <height>91</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
    </widget>
    <widget class="QLabel" name="label_13">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>260</y>
       <width>91</width>
       <height>16</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
      </font>
     </property>
     <property name="text">
      <string>进程2: 视频处理</string>
     </property>
    </widget>
    <widget class="QTextEdit" name="log_output_00">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>40</y>
       <width>331</width>
       <height>91</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
    </widget>
    <widget class="QLabel" name="label_12">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>140</y>
       <width>81</width>
       <height>16</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
      </font>
     </property>
     <property name="text">
      <string>进程1: 主通信</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_14">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>380</y>
       <width>111</width>
       <height>16</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
      </font>
     </property>
     <property name="text">
      <string>进程3: 视频信号获取</string>
     </property>
    </widget>
    <widget class="QTextEdit" name="log_output_01">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>160</y>
       <width>331</width>
       <height>91</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
    </widget>
   </widget>
   <widget class="QFrame" name="parameteComponentFrame">
    <property name="geometry">
     <rect>
      <x>530</x>
      <y>60</y>
      <width>371</width>
      <height>471</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::Shape::NoFrame</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Shadow::Raised</enum>
    </property>
    <widget class="QFrame" name="advancedSettingsFrame">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>371</width>
       <height>461</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgba(0, 0, 0, 0);
border-color: rgba(0, 0, 0, 0);</string>
     </property>
     <widget class="QComboBox" name="detectionTargetComboBox">
      <property name="geometry">
       <rect>
        <x>100</x>
        <y>10</y>
        <width>111</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>10</pointsize>
        <bold>false</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgb(44, 44, 44);</string>
      </property>
      <item>
       <property name="text">
        <string>0</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>1</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>2</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>3</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>4</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>5</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>ALL</string>
       </property>
      </item>
     </widget>
     <widget class="QLabel" name="detectionTargetLabel">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>10</y>
        <width>71</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
      </property>
      <property name="text">
       <string>检测目标:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QCheckBox" name="auxiliaryPressureGunCheckBox_4">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>310</y>
        <width>91</width>
        <height>20</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>自动急停</string>
      </property>
     </widget>
     <widget class="QCheckBox" name="automatic_trigger_switchCheckBox">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>340</y>
        <width>91</width>
        <height>20</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>自动扳机</string>
      </property>
     </widget>
     <widget class="QToolButton" name="auxiliaryPressureGunToolButton_4">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>120</x>
        <y>310</y>
        <width>21</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>...</string>
      </property>
     </widget>
     <widget class="QToolButton" name="automatic_trigger_switchToolButton">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>120</x>
        <y>340</y>
        <width>21</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>...</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_26">
      <property name="geometry">
       <rect>
        <x>220</x>
        <y>15</y>
        <width>21</width>
        <height>21</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="toolTip">
       <string>根据模型进行调整</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="pixmap">
       <pixmap>ico/warningtriangle_87535-modified.png</pixmap>
      </property>
      <property name="scaledContents">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="QLabel" name="aimingRangeLabel_3">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>50</y>
        <width>81</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
      </property>
      <property name="text">
       <string>瞄准偏移X:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QLCDNumber" name="offset_centerxNumber">
      <property name="geometry">
       <rect>
        <x>295</x>
        <y>55</y>
        <width>64</width>
        <height>23</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::Box</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Plain</enum>
      </property>
      <property name="segmentStyle">
       <enum>QLCDNumber::SegmentStyle::Flat</enum>
      </property>
     </widget>
     <widget class="QSlider" name="offset_centerxVerticalSlider">
      <property name="geometry">
       <rect>
        <x>130</x>
        <y>57</y>
        <width>161</width>
        <height>20</height>
       </rect>
      </property>
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
     </widget>
     <widget class="QLabel" name="aimingRangeLabel_4">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>140</y>
        <width>81</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
      </property>
      <property name="text">
       <string>瞄准偏移Y:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QSlider" name="offset_centeryVerticalSlider">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>79</y>
        <width>20</width>
        <height>181</height>
       </rect>
      </property>
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
     </widget>
     <widget class="QLCDNumber" name="offset_centeryNumber">
      <property name="geometry">
       <rect>
        <x>295</x>
        <y>160</y>
        <width>64</width>
        <height>23</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::Box</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Plain</enum>
      </property>
      <property name="segmentStyle">
       <enum>QLCDNumber::SegmentStyle::Flat</enum>
      </property>
     </widget>
     <widget class="QLabel" name="label_27">
      <property name="geometry">
       <rect>
        <x>145</x>
        <y>80</y>
        <width>131</width>
        <height>181</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="pixmap">
       <pixmap>ico/body_photo.png</pixmap>
      </property>
      <property name="scaledContents">
       <bool>true</bool>
      </property>
     </widget>
     <widget class="QLabel" name="detectionTargetLabel_2">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>270</y>
        <width>71</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
      </property>
      <property name="text">
       <string>移动方式:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QComboBox" name="mobileModeQComboBox">
      <property name="geometry">
       <rect>
        <x>100</x>
        <y>270</y>
        <width>111</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>10</pointsize>
        <bold>false</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgb(44, 44, 44);</string>
      </property>
      <item>
       <property name="text">
        <string>win32</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>飞易来</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>KmBoxNet</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>Logitech</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>mouse</string>
       </property>
      </item>
     </widget>
     <widget class="QCheckBox" name="jumpSuppressionCheckBox">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>370</y>
        <width>91</width>
        <height>20</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="toolTip">
       <string>演戏，目标死亡后防止瞄准转移</string>
      </property>
      <property name="text">
       <string>跳变抑制</string>
      </property>
     </widget>
     <widget class="QLCDNumber" name="jumpSuppressionNumber">
      <property name="geometry">
       <rect>
        <x>295</x>
        <y>370</y>
        <width>64</width>
        <height>23</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::Box</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Plain</enum>
      </property>
      <property name="segmentStyle">
       <enum>QLCDNumber::SegmentStyle::Flat</enum>
      </property>
     </widget>
     <widget class="QSlider" name="jumpSuppressionVerticalSlider">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>370</y>
        <width>181</width>
        <height>20</height>
       </rect>
      </property>
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
     </widget>
     <widget class="QCheckBox" name="smoothAimingCheckBox_5">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>400</y>
        <width>91</width>
        <height>20</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>鼠标连点</string>
      </property>
     </widget>
     <widget class="QToolButton" name="smoothAimingToolButton_5">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>120</x>
        <y>400</y>
        <width>21</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>...</string>
      </property>
     </widget>
    </widget>
    <widget class="QFrame" name="softwareInformationFrame">
     <property name="geometry">
      <rect>
       <x>840</x>
       <y>0</y>
       <width>371</width>
       <height>461</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgba(0, 0, 0, 0);
border-color: rgba(0, 0, 0, 0);</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::Box</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <property name="lineWidth">
      <number>0</number>
     </property>
     <widget class="QLabel" name="label_2">
      <property name="geometry">
       <rect>
        <x>135</x>
        <y>10</y>
        <width>101</width>
        <height>41</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>18</pointsize>
       </font>
      </property>
      <property name="text">
       <string>公告</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
     </widget>
     <widget class="QTextEdit" name="announcement">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>60</y>
        <width>311</width>
        <height>181</height>
       </rect>
      </property>
      <property name="markdown">
       <string>在这里显示公告！！！

</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_3">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>260</y>
        <width>41</width>
        <height>21</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>渠道：</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_4">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>300</y>
        <width>91</width>
        <height>21</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>当前版本：</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_6">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>340</y>
        <width>71</width>
        <height>21</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>构建时间：</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_16">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>430</y>
        <width>41</width>
        <height>21</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>作者：</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_20">
      <property name="geometry">
       <rect>
        <x>70</x>
        <y>430</y>
        <width>61</width>
        <height>21</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Bauhaus 93</family>
        <pointsize>14</pointsize>
       </font>
      </property>
      <property name="text">
       <string>hoho</string>
      </property>
     </widget>
     <widget class="QLabel" name="channelLabel">
      <property name="geometry">
       <rect>
        <x>80</x>
        <y>260</y>
        <width>181</width>
        <height>21</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>内部预览版/预览版/正式版</string>
      </property>
     </widget>
     <widget class="QLabel" name="versionLabel">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>300</y>
        <width>300</width>
        <height>21</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>NowVersion</string>
      </property>
     </widget>
     <widget class="QLabel" name="versionDateLabel">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>340</y>
        <width>200</width>
        <height>21</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>VERSION_DATE</string>
      </property>
     </widget>
     <widget class="QLabel" name="label_24">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>320</y>
        <width>71</width>
        <height>21</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>最新版本：</string>
      </property>
     </widget>
     <widget class="QLabel" name="latestVersionLabel">
      <property name="geometry">
       <rect>
        <x>110</x>
        <y>320</y>
        <width>650</width>
        <height>21</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>11</pointsize>
       </font>
      </property>
      <property name="text">
       <string>LatestVersion</string>
      </property>
     </widget>
    </widget>
    <widget class="QFrame" name="basicSettingsFrame">
     <property name="geometry">
      <rect>
       <x>450</x>
       <y>0</y>
       <width>371</width>
       <height>461</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
     </property>
     <widget class="QComboBox" name="triggerMethodComboBox">
      <property name="geometry">
       <rect>
        <x>100</x>
        <y>50</y>
        <width>111</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>10</pointsize>
        <bold>false</bold>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgb(44, 44, 44);</string>
      </property>
      <item>
       <property name="text">
        <string>按下</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>切换</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>shift+按下</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>自动</string>
       </property>
      </item>
     </widget>
     <widget class="QCheckBox" name="aimBotCheckBox">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>10</y>
        <width>101</width>
        <height>21</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Arial Rounded MT</family>
        <pointsize>12</pointsize>
        <bold>true</bold>
       </font>
      </property>
      <property name="whatsThis">
       <string>自瞄开关</string>
      </property>
      <property name="text">
       <string>Aimbot</string>
      </property>
      <property name="iconSize">
       <size>
        <width>24</width>
        <height>24</height>
       </size>
      </property>
      <property name="autoRepeatDelay">
       <number>310</number>
      </property>
     </widget>
     <widget class="QLabel" name="triggerMethodLabel">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>50</y>
        <width>91</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
      </property>
      <property name="text">
       <string>触发方式:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QCheckBox" name="sideButtonCheckBox">
      <property name="geometry">
       <rect>
        <x>180</x>
        <y>10</y>
        <width>171</width>
        <height>20</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>鼠标侧键瞄准开关</string>
      </property>
     </widget>
     <widget class="QLabel" name="triggerHotkeyLabel">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>90</y>
        <width>200</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
      </property>
      <property name="text">
       <string>触发热键:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QPushButton" name="HotkeyPushButton">
      <property name="geometry">
       <rect>
        <x>100</x>
        <y>90</y>
        <width>111</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>10</pointsize>
        <bold>false</bold>
       </font>
      </property>
      <property name="text">
       <string>HotKeyName</string>
      </property>
     </widget>
     <widget class="QCheckBox" name="auxiliaryPressureGunCheckBox">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>140</y>
        <width>91</width>
        <height>20</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>辅助压枪</string>
      </property>
     </widget>
     <widget class="QCheckBox" name="smoothAimingCheckBox">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>180</y>
        <width>91</width>
        <height>20</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>平滑瞄准</string>
      </property>
     </widget>
     <widget class="QToolButton" name="auxiliaryPressureGunToolButton">
      <property name="geometry">
       <rect>
        <x>120</x>
        <y>140</y>
        <width>21</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>...</string>
      </property>
     </widget>
     <!-- 压枪力度滑动条 -->
     <widget class="QSlider" name="recoilStrengthHorizontalSlider">
       <property name="geometry">
        <rect>
         <x>120</x>
         <y>140</y>
         <width>161</width>
         <height>18</height>
        </rect>
       </property>
       <property name="maximum">
        <number>100</number>
       </property>
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
     </widget>
     <!-- 压枪力度数值显示 -->
     <widget class="QLCDNumber" name="recoilStrengthLcdNumber">
       <property name="geometry">
        <rect>
         <x>300</x>
         <y>138</y>
         <width>64</width>
         <height>23</height>
        </rect>
       </property>
       <property name="frameShape">
        <enum>QFrame::Shape::Box</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Shadow::Plain</enum>
       </property>
       <property name="segmentStyle">
        <enum>QLCDNumber::SegmentStyle::Flat</enum>
      </property>
     </widget>
     <widget class="QToolButton" name="smoothAimingToolButton">
      <property name="geometry">
       <rect>
        <x>120</x>
        <y>180</y>
        <width>21</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>...</string>
      </property>
     </widget>
     <widget class="QToolButton" name="automaticTriggerToolButton">
      <property name="geometry">
       <rect>
        <x>120</x>
        <y>220</y>
        <width>21</width>
        <height>21</height>
       </rect>
      </property>
      <property name="text">
       <string>...</string>
      </property>
     </widget>
     <widget class="QCheckBox" name="automaticTriggerCheckBox">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>220</y>
        <width>91</width>
        <height>20</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="text">
       <string>自动扳机</string>
      </property>
     </widget>
     <widget class="QLabel" name="lockSpeedXLabel">
      <property name="geometry">
       <rect>
        <x>15</x>
        <y>245</y>
        <width>81</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
      </property>
      <property name="text">
       <string>瞄准速度X:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="aimingRangeLabel">
      <property name="geometry">
       <rect>
        <x>15</x>
        <y>305</y>
        <width>71</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
      </property>
      <property name="text">
       <string>瞄准范围:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QSlider" name="lockSpeedXHorizontalSlider">
      <property name="geometry">
       <rect>
        <x>95</x>
        <y>252</y>
        <width>191</width>
        <height>18</height>
       </rect>
      </property>
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
     </widget>
     <widget class="QSlider" name="aimRangeHorizontalSlider">
      <property name="geometry">
       <rect>
        <x>95</x>
        <y>312</y>
        <width>191</width>
        <height>18</height>
       </rect>
      </property>
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
     </widget>
     <widget class="QLCDNumber" name="lockSpeedXLcdNumber">
      <property name="geometry">
       <rect>
        <x>290</x>
        <y>250</y>
        <width>64</width>
        <height>23</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::Box</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Plain</enum>
      </property>
      <property name="segmentStyle">
       <enum>QLCDNumber::SegmentStyle::Flat</enum>
      </property>
     </widget>
     <widget class="QLCDNumber" name="aimRangeLcdNumber">
      <property name="geometry">
       <rect>
        <x>290</x>
        <y>310</y>
        <width>64</width>
        <height>23</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::Box</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Plain</enum>
      </property>
      <property name="segmentStyle">
       <enum>QLCDNumber::SegmentStyle::Flat</enum>
      </property>
     </widget>
     <widget class="QSlider" name="aimRangeHorizontalSlider_2">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>95</x>
        <y>342</y>
        <width>191</width>
        <height>18</height>
       </rect>
      </property>
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
     </widget>
     <widget class="QLCDNumber" name="aimRangeLcdNumber_2">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>290</x>
        <y>340</y>
        <width>64</width>
        <height>23</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::Box</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Plain</enum>
      </property>
      <property name="segmentStyle">
       <enum>QLCDNumber::SegmentStyle::Flat</enum>
      </property>
     </widget>
     <widget class="QLabel" name="aimingRangeLabel_2">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>15</x>
        <y>335</y>
        <width>71</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
      </property>
      <property name="text">
       <string>移速补偿:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QLCDNumber" name="aimRangeLcdNumber_3">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>290</x>
        <y>370</y>
        <width>64</width>
        <height>23</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::Box</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Plain</enum>
      </property>
      <property name="segmentStyle">
       <enum>QLCDNumber::SegmentStyle::Flat</enum>
      </property>
     </widget>
     <widget class="QSlider" name="aimRangeHorizontalSlider_3">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>95</x>
        <y>372</y>
        <width>191</width>
        <height>18</height>
       </rect>
      </property>
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
     </widget>
     <widget class="QLabel" name="aimingRangeLabel_5">
      <property name="enabled">
       <bool>false</bool>
      </property>
      <property name="geometry">
       <rect>
        <x>15</x>
        <y>365</y>
        <width>71</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
      </property>
      <property name="text">
       <string>减速区域:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
      </property>
     </widget>
     <widget class="QSlider" name="lockSpeedYHorizontalSlider">
      <property name="geometry">
       <rect>
        <x>95</x>
        <y>280</y>
        <width>191</width>
        <height>18</height>
       </rect>
      </property>
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
     </widget>
     <widget class="QLCDNumber" name="lockSpeedYLcdNumber">
      <property name="geometry">
       <rect>
        <x>290</x>
        <y>280</y>
        <width>64</width>
        <height>23</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::Box</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Plain</enum>
      </property>
      <property name="segmentStyle">
       <enum>QLCDNumber::SegmentStyle::Flat</enum>
      </property>
     </widget>
     <widget class="QLabel" name="lockSpeedYLabel">
      <property name="geometry">
       <rect>
        <x>15</x>
        <y>275</y>
        <width>81</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
      </property>
      <property name="text">
       <string>瞄准速度Y:</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
      </property>
     </widget>
    </widget>
   </widget>
   <zorder>settingWidget_2</zorder>
   <zorder>settingWidget</zorder>
   <zorder>show_video</zorder>
   <zorder>OpVideoButton</zorder>
   <zorder>OpYoloButton</zorder>
   <zorder>settingsYoloButton</zorder>
   <zorder>closeMessageWidget</zorder>
   <zorder>widget</zorder>
   <zorder>frame_3</zorder>
   <zorder>statusDisplayWidget</zorder>
   <zorder>parameteComponentFrame</zorder>
   <zorder>overlay</zorder>
   <zorder>settingsPanel</zorder>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
