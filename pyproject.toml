[project]
name = "rookieai"
version = "0.1.0"
description = ""
authors = [
    {name = "Your Name",email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "opencv-python (>=*********,<*******)",
    "numpy (>=1.23.0,<3.0.0)",
    "pyautogui (>=0.9.54,<0.10.0)",
    "pywin32 (>=308,<309)",
    "mss (==6.1.0)",
    "ultralytics (>=8.3.65,<9.0.0)",
    "pillow (>=11.1.0,<12.0.0)",
    "chardet (>=5.2.0,<6.0.0)",
    "dxcam (>=0.0.5,<0.0.6)",
    "requests (>=2.32.3,<3.0.0)",
    "mouse (>=0.7.1,<0.8.0)",
    "psutil (>=6.1.1,<7.0.0)",
    "pyqt6 (>=6.8.0,<7.0.0)",
    "pynput (>=1.7.7,<2.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = "ali"
url = "https://mirrors.aliyun.com/pypi/simple/"
priority = "primary"

