#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Python进程数量
"""

import psutil
import os
import sys

def check_python_processes():
    """检查当前运行的Python进程"""
    print("检查Python进程...")
    print("=" * 50)
    
    current_pid = os.getpid()
    python_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                python_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cmdline': cmdline,
                    'is_current': proc.info['pid'] == current_pid
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"找到 {len(python_processes)} 个Python进程:")
    print()
    
    for i, proc in enumerate(python_processes, 1):
        status = " (当前进程)" if proc['is_current'] else ""
        print(f"{i}. PID: {proc['pid']}{status}")
        print(f"   名称: {proc['name']}")
        print(f"   命令行: {proc['cmdline'][:100]}{'...' if len(proc['cmdline']) > 100 else ''}")
        print()
    
    # 分析hohoai.py相关的进程
    hohoai_processes = [p for p in python_processes if 'hohoai.py' in p['cmdline']]
    
    if hohoai_processes:
        print(f"hohoai.py 相关进程: {len(hohoai_processes)} 个")
        for proc in hohoai_processes:
            print(f"  - PID: {proc['pid']}")
        
        if len(hohoai_processes) > 1:
            print("⚠️  警告: 发现多个hohoai.py进程!")
            print("这可能是由以下原因造成的:")
            print("1. 自动机器人的按键监听器")
            print("2. overlay进程")
            print("3. 其他子进程")
        else:
            print("✅ 只有一个hohoai.py进程，正常")
    else:
        print("没有找到hohoai.py相关进程")
    
    return len(python_processes), len(hohoai_processes)

def monitor_processes(duration=30):
    """监控进程变化"""
    print(f"\n开始监控进程变化 ({duration}秒)...")
    print("=" * 50)
    
    import time
    start_time = time.time()
    last_count = 0
    
    while time.time() - start_time < duration:
        total_python, hohoai_count = check_python_processes()
        
        if total_python != last_count:
            print(f"[{time.strftime('%H:%M:%S')}] Python进程数量变化: {last_count} -> {total_python}")
            last_count = total_python
        
        time.sleep(2)
        print(".", end="", flush=True)
    
    print(f"\n监控结束")

def main():
    """主函数"""
    print("Python进程检查工具")
    print("=" * 50)
    print("此工具用于检查是否有多个Python进程运行")
    print("特别是检查hohoai.py是否创建了额外的进程")
    print()
    
    # 检查当前进程
    check_python_processes()
    
    # 询问是否要监控
    try:
        choice = input("\n是否要监控进程变化? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            monitor_processes()
    except KeyboardInterrupt:
        print("\n\n程序结束")

if __name__ == "__main__":
    main()
