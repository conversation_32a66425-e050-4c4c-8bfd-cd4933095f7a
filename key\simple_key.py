import os
import json
import time
import hashlib
from flask import Flask, request, jsonify

DB_FILE = os.path.join("key", "card_db.json")

app = Flask(__name__)

def load_db():
    # 確保資料夾存在
    db_dir = os.path.dirname(DB_FILE)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)

    if not os.path.exists(DB_FILE):
        with open(DB_FILE, "w", encoding="utf-8") as f:
            json.dump({}, f, ensure_ascii=False)
    with open(DB_FILE, "r") as f:
        return json.load(f)

def save_db(db):
    with open(DB_FILE, "w") as f:
        json.dump(db, f, indent=2)

@app.route('/api/check_card', methods=['POST'])
def check_card():
    """验证卡密 API"""
    try:
        db = load_db()
        card_key = request.json.get("card_key")
        now = time.time()
        
        print(f"[DEBUG] 收到卡密验证请求: {card_key}")
        print(f"[DEBUG] 数据库中的卡密: {list(db.keys())}")
        
        for k, v in db.items():
            stored_key = v["plain"]
            print(f"[DEBUG] 存储的卡密: {stored_key}")
            
            if stored_key == card_key and v["valid"] and now < v["expire_at"]:
                print(f"[DEBUG] 卡密验证成功")
                return jsonify({"valid": True, "msg": "卡密有效"})
            else:
                print(f"[DEBUG] 卡密验证失败: stored={stored_key}, input={card_key}, valid={v['valid']}, expired={now >= v['expire_at']}")
        
        return jsonify({"valid": False, "msg": "卡密无效或已过期"})
    except Exception as e:
        print(f"[ERROR] 验证过程中发生错误: {e}")
        return jsonify({"valid": False, "msg": f"验证错误: {str(e)}"})

@app.route('/')
def index():
    return "卡密验证服务器运行中"

if __name__ == "__main__":
    print("启动简化版卡密验证服务器...")
    app.run(host='0.0.0.0', port=5000, debug=True) 