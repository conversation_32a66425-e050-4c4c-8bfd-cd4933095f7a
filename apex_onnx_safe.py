#!/usr/bin/env python3
"""
安全使用 Apex ONNX 模型的代码
解决 CUDA 依赖问题
"""

import os
import cv2
import numpy as np
import time

def create_apex_onnx_model(model_path="MY_apex/YOLOv8s_apex_teammate_enemy_gpu.onnx", force_cpu=False):
    """
    创建 Apex ONNX 模型，自动处理 GPU/CPU 兼容性
    
    Args:
        model_path (str): ONNX 模型路径
        force_cpu (bool): 是否强制使用 CPU
    
    Returns:
        ApexONNXModel: 模型实例
    """
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    return ApexONNXModel(model_path, force_cpu=force_cpu)

class ApexONNXModel:
    """Apex ONNX 模型封装类，自动处理 GPU/CPU 兼容性"""
    
    def __init__(self, model_path, force_cpu=False):
        self.model_path = model_path
        self.force_cpu = force_cpu
        self.session = None
        self.input_name = None
        self.output_names = None
        self.using_gpu = False
        
        self._load_model()
    
    def _load_model(self):
        """加载模型，自动处理 GPU/CPU 兼容性"""
        import onnxruntime as ort
        
        if self.force_cpu:
            print("🔧 强制使用 CPU 模式")
            providers = ['CPUExecutionProvider']
        else:
            print("🔍 尝试 GPU 模式...")
            providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        
        try:
            # 尝试创建会话
            self.session = ort.InferenceSession(self.model_path, providers=providers)
            
            # 检查实际使用的提供程序
            used_provider = self.session.get_providers()[0]
            
            if 'CUDA' in used_provider:
                print(f"✅ 成功使用 GPU 加速: {used_provider}")
                self.using_gpu = True
            else:
                print(f"✅ 使用 CPU 推理: {used_provider}")
                self.using_gpu = False
            
            # 获取输入输出信息
            self.input_name = self.session.get_inputs()[0].name
            self.output_names = [output.name for output in self.session.get_outputs()]
            
            # 显示模型信息
            input_shape = self.session.get_inputs()[0].shape
            print(f"📊 输入形状: {input_shape}")
            print(f"📊 输出数量: {len(self.output_names)}")
            
        except Exception as e:
            if not self.force_cpu:
                print(f"❌ GPU 模式失败: {e}")
                print("🔄 回退到 CPU 模式...")
                self.force_cpu = True
                self._load_model()  # 递归调用，强制使用 CPU
            else:
                print(f"❌ CPU 模式也失败: {e}")
                raise
    
    def preprocess(self, image):
        """预处理图像"""
        # 调整大小
        if image.shape[:2] != (640, 640):
            image = cv2.resize(image, (640, 640))
        
        # BGR -> RGB
        if len(image.shape) == 3 and image.shape[2] == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 归一化
        image = image.astype(np.float32) / 255.0
        
        # 转换维度 (H, W, C) -> (1, C, H, W)
        image = np.transpose(image, (2, 0, 1))
        image = np.expand_dims(image, axis=0)
        
        return image
    
    def predict(self, image):
        """执行推理"""
        if self.session is None:
            raise RuntimeError("模型未加载")
        
        # 预处理
        input_data = self.preprocess(image)
        
        # 推理
        outputs = self.session.run(self.output_names, {self.input_name: input_data})
        
        return outputs
    
    def postprocess(self, outputs, conf_threshold=0.3, img_width=640, img_height=640):
        """
        后处理推理结果
        
        Args:
            outputs: 模型输出
            conf_threshold: 置信度阈值
            img_width: 原图宽度
            img_height: 原图高度
        
        Returns:
            list: [(x1, y1, x2, y2, conf, class_id), ...]
        """
        if not outputs:
            return []
        
        # 获取输出 (1, 7, 8400)
        output = outputs[0][0]  # (7, 8400)
        output = output.transpose()  # (8400, 7)
        
        detections = []
        for detection in output:
            # detection: [x, y, w, h, conf, class1, class2, ...]
            x, y, w, h = detection[:4]
            conf = detection[4]
            
            if conf > conf_threshold:
                # 转换坐标
                x1 = (x - w / 2) * img_width / 640
                y1 = (y - h / 2) * img_height / 640
                x2 = (x + w / 2) * img_width / 640
                y2 = (y + h / 2) * img_height / 640
                
                # 获取类别
                class_scores = detection[5:]
                class_id = np.argmax(class_scores)
                
                detections.append((x1, y1, x2, y2, conf, class_id))
        
        return detections

def test_apex_onnx():
    """测试 Apex ONNX 模型"""
    print("🧪 测试 Apex ONNX 模型")
    print("=" * 40)
    
    try:
        # 创建模型（自动处理 GPU/CPU）
        model = create_apex_onnx_model()
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        print("\n🔄 执行推理测试...")
        
        # 推理测试
        start_time = time.time()
        outputs = model.predict(test_image)
        end_time = time.time()
        
        inference_time = (end_time - start_time) * 1000
        print(f"✅ 推理成功! 耗时: {inference_time:.2f} ms")
        
        # 后处理
        detections = model.postprocess(outputs)
        print(f"📊 检测结果: {len(detections)} 个目标")
        
        # 性能测试
        print("\n⏱️  性能测试 (50次推理)...")
        start_time = time.time()
        for i in range(50):
            outputs = model.predict(test_image)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 50 * 1000
        fps = 1000 / avg_time
        
        print(f"📊 平均推理时间: {avg_time:.2f} ms")
        print(f"📊 理论 FPS: {fps:.1f}")
        
        if model.using_gpu:
            print("🎮 GPU 加速正常工作!")
        else:
            print("💻 CPU 模式工作正常")
        
        print("\n🎉 测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 Apex ONNX 模型安全使用工具")
    print("=" * 50)
    
    # 检查模型文件
    model_path = "MY_apex/YOLOv8s_apex_teammate_enemy_gpu.onnx"
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("💡 请确保已转换 ONNX 模型")
        return False
    
    print(f"✅ 找到模型文件: {model_path}")
    
    # 测试模型
    if test_apex_onnx():
        print("\n💡 集成建议:")
        print("1. 使用 ApexONNXModel 类替代原来的 YOLO 加载")
        print("2. 自动处理 GPU/CPU 兼容性问题")
        print("3. 保持高性能推理")
        
        print("\n📝 使用示例:")
        print("```python")
        print("from apex_onnx_safe import create_apex_onnx_model")
        print("model = create_apex_onnx_model()")
        print("outputs = model.predict(image)")
        print("detections = model.postprocess(outputs)")
        print("```")
        
        return True
    else:
        print("\n❌ 测试失败，请检查依赖安装")
        return False

if __name__ == "__main__":
    main()
