<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .table {
            background-color: white;
        }
        .encrypted-text {
            font-family: monospace;
        }
        .btn-sm {
            padding: 0.15rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>卡密管理系统</h2>
            <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">退出登录</a>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">添加新卡密</h5>
            </div>
            <div class="card-body">
                <form method="post" action="{{ url_for('add_card') }}" class="row g-3">
                    <div class="col-md-6">
                        <input type="text" name="key" class="form-control" placeholder="输入卡密" required>
                    </div>
                    <div class="col-md-3">
                        <input type="number" name="minutes" class="form-control" placeholder="有效分钟数" required>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary w-100">添加卡密</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">卡密列表</h5>
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>卡密</th>
                            <th>状态</th>
                            <th>剩余时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for key, info in cards.items() %}
                        <tr>
                            <td>
                                {% if info.plain.startswith('enc:') %}
                                    <span class="encrypted-text" data-encrypted="{{ info.plain }}">
                                        {{ info.plain[4:16] }}...
                                    </span>
                                    <button class="btn btn-sm btn-outline-primary view-btn" data-encrypted="{{ info.plain }}">
                                        <i class="bi bi-eye"></i> 查看
                                    </button>
                                {% else %}
                                    {{ info.plain }}
                                {% endif %}
                            </td>
                            <td>
                                {% if info.valid %}
                                    <span class="badge bg-success">有效</span>
                                {% else %}
                                    <span class="badge bg-danger">已禁用</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if info.left_minutes > 0 %}
                                    {{ info.left_minutes }} 分钟
                                {% else %}
                                    已过期 ({{ info.expire_at|datetimeformat }})
                                {% endif %}
                            </td>
                            <td>
                                {% if info.valid %}
                                    <a href="{{ url_for('disable_card', key=key) }}" class="btn btn-sm btn-warning">禁用</a>
                                {% else %}
                                    <a href="{{ url_for('enable_card', key=key) }}" class="btn btn-sm btn-success">启用</a>
                                {% endif %}
                                <button class="btn btn-sm btn-danger" onclick="deleteCard('{{ key }}')">删除</button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 解密弹窗 -->
    <div class="modal fade" id="decryptModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">卡密详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>卡密：<code id="decryptedKey"></code></p>
                    <button id="copyBtn" class="btn btn-primary">复制卡密</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 解密并显示卡密
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const encrypted = this.getAttribute('data-encrypted');
                fetch('/decrypt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ encrypted: encrypted })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.decrypted) {
                        document.getElementById('decryptedKey').textContent = data.decrypted;
                        const modal = new bootstrap.Modal(document.getElementById('decryptModal'));
                        modal.show();
                    } else {
                        alert('解密失败：' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('解密请求失败:', error);
                    alert('解密请求失败: ' + error.message);
                });
            });
        });

        // 复制卡密
        document.getElementById('copyBtn')?.addEventListener('click', function() {
            const key = document.getElementById('decryptedKey').textContent;
            navigator.clipboard.writeText(key).then(() => {
                this.textContent = '已复制！';
                setTimeout(() => {
                    this.textContent = '复制卡密';
                }, 2000);
            });
        });

        // 删除卡密
        function deleteCard(key) {
            if (confirm('确定要删除此卡密吗？此操作不可恢复！')) {
                fetch('/delete_card', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ key: key })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除过程中出错');
                });
            }
        }

        // 自动刷新页面（每60秒）
        setTimeout(() => {
            window.location.reload();
        }, 60000);
    </script>
</body>
</html> 