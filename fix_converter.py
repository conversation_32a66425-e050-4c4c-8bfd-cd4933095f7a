#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版 PyTorch 转 ONNX 转换器
解决新版 PyTorch 兼容性问题
使用方法: python fix_converter.py model.pt
"""

import torch
import torch.onnx
import os
import sys

def convert_pt_to_onnx(pt_path, onnx_path=None, input_size=(1, 3, 640, 640)):
    """将 PyTorch 模型转换为 ONNX"""
    
    if not os.path.exists(pt_path):
        print(f"❌ 找不到文件: {pt_path}")
        return False
    
    if onnx_path is None:
        onnx_path = pt_path.replace('.pt', '.onnx').replace('.pth', '.onnx')
    
    try:
        print(f"📁 加载模型: {pt_path}")
        
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"🖥️  设备: {device}")
        
        # 修复版本：处理新版 PyTorch 的兼容性问题
        model = None
        try:
            # 方法1: 直接使用 weights_only=False
            model = torch.load(pt_path, map_location=device, weights_only=False)
            print("✅ 兼容模式加载成功")
        except Exception as e:
            if "weights_only" in str(e) or "WeightsUnpickler" in str(e):
                print("🔧 检测到新版 PyTorch，尝试安全模式...")
                try:
                    # 方法2: 添加安全全局类
                    import torch.serialization
                    safe_globals = [
                        'ultralytics.nn.tasks.DetectionModel',
                        'ultralytics.nn.tasks.ClassificationModel', 
                        'ultralytics.nn.tasks.SegmentationModel',
                        'ultralytics.nn.modules.head.Detect',
                        'ultralytics.nn.modules.head.Classify',
                        'ultralytics.nn.modules.head.Segment',
                        'ultralytics.nn.modules.conv.Conv',
                        'ultralytics.nn.modules.block.C2f',
                        'ultralytics.nn.modules.block.SPPF'
                    ]
                    torch.serialization.add_safe_globals(safe_globals)
                    model = torch.load(pt_path, map_location=device, weights_only=True)
                    print("✅ 安全模式加载成功")
                except Exception as e2:
                    print(f"⚠️  安全模式失败: {e2}")
                    # 方法3: 传统方式
                    model = torch.load(pt_path, map_location=device)
                    print("✅ 传统模式加载成功")
            else:
                raise e
        
        if model is None:
            print("❌ 模型加载失败")
            return False
        
        # 处理不同的模型格式
        if isinstance(model, dict):
            if 'model' in model:
                model = model['model']
            elif 'state_dict' in model:
                print("❌ 需要完整的模型文件，不是 state_dict")
                return False
        
        model.eval()
        dummy_input = torch.randn(input_size).to(device)
        
        print(f"🔄 转换中... 输入尺寸: {input_size}")
        
        torch.onnx.export(
            model, 
            dummy_input, 
            onnx_path,
            export_params=True, 
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'], 
            output_names=['output'],
            dynamic_axes={
                'input': {0: 'batch_size'}, 
                'output': {0: 'batch_size'}
            }
        )
        
        print(f"✅ 转换成功! 输出: {onnx_path}")
        
        pt_size = os.path.getsize(pt_path) / (1024*1024)
        onnx_size = os.path.getsize(onnx_path) / (1024*1024)
        print(f"📊 原始: {pt_size:.1f}MB → ONNX: {onnx_size:.1f}MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        print("\n💡 可能的解决方案:")
        print("1. 确保模型文件完整且未损坏")
        print("2. 检查 PyTorch 版本兼容性")
        print("3. 如果是 YOLOv8 模型，确保环境正确")
        return False

def main():
    print("🚀 修复版 PyTorch → ONNX 转换器")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("使用方法: python fix_converter.py model.pt")
        print("示例: python fix_converter.py best.pt")
        print("专门解决新版 PyTorch 兼容性问题")
        return
    
    pt_path = sys.argv[1]
    onnx_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 默认输入尺寸
    input_size = (1, 3, 640, 640)
    
    success = convert_pt_to_onnx(pt_path, onnx_path, input_size)
    
    if success:
        print("\n🎉 转换完成!")
    else:
        print("\n💥 转换失败!")

if __name__ == "__main__":
    main()
