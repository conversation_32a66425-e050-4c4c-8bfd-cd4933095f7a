import os
import sys
import subprocess
import time
from pathlib import Path
import json, base64

from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

# -------- 检查第三方依赖 --------
missing = []
for pkg in ("flask", "Crypto"):
    try:
        __import__(pkg.split(".")[0])
    except ImportError:
        missing.append(pkg)

if missing:
    print("缺少依赖: " + ", ".join(missing))
    print("请先执行: pip install flask pycryptodome")
    sys.exit(1)

try:
    import requests
except ImportError:
    print("缺少 requests 库，请执行: pip install requests")
    sys.exit(1)

PROJECT_ROOT = Path(__file__).resolve().parent
FULL_KEY = PROJECT_ROOT / "key" / "full_key.py"
if not FULL_KEY.exists():
    print(f"找不到 {FULL_KEY}，请确认目录结构正确！")
    sys.exit(1)

# -------- 环境变量设置 --------
def prompt_env(var, default=""):
    val = os.environ.get(var)
    if not val:
        prompt = f"请输入 {var}" + (f" [默认 {default}]" if default else " (必填)") + ": "
        val = input(prompt).strip() or default
    if not val:
        print(f"{var} 不能为空，脚本终止。")
        sys.exit(1)
    return val

ADMIN_USER = prompt_env("ADMIN_USER", "admin")
ADMIN_PASS = prompt_env("ADMIN_PASS", "adminpass")
DEFAULT_SERVER = "http://************:5000"
DEFAULT_TOKEN = "3f8c1a9e4b9d63a01e6d2c14c956aa7b"
API_TOKEN = prompt_env("API_TOKEN", DEFAULT_TOKEN)

# 将变量写入 os.environ，供子进程使用
os.environ.update({
    "ADMIN_USER": ADMIN_USER,
    "ADMIN_PASS": ADMIN_PASS,
    "API_TOKEN": API_TOKEN,
})

# -------- 检测服务器是否已在运行 --------
SERVER_URL = DEFAULT_SERVER

def server_alive():
    try:
        requests.get(SERVER_URL, timeout=2)
        return True
    except Exception:
        return False

if server_alive():
    print("✔ full_key.py 已在运行，无需重复启动。")
    sys.exit(0)

print("未检测到服务器，正在启动 full_key.py … …")

proc = subprocess.Popen([sys.executable, str(FULL_KEY)])

# 等待启动
for i in range(10):
    if server_alive():
        print("✔ 服务器已启动并可用 →", SERVER_URL)
        break
    time.sleep(1)
else:
    print("启动超时，请检查日志输出。")
    proc.terminate()
    sys.exit(1)

print("按 Ctrl+C 可停止服务器，或关闭此窗口。")

# 保持當前視窗，直到使用者 Ctrl+C
try:
    proc.wait()
except KeyboardInterrupt:
    print("\n检测到退出信号，正在关闭服务器 … …")
    proc.terminate()
    proc.wait()
    sys.exit(0)


def pretty(data: dict):
    print(json.dumps(data, ensure_ascii=False, indent=2))


def ask(prompt: str, default: str | None = None):
    s = input(f"{prompt} [{default or ''}]: ").strip()
    return s or (default or "")


# --- AES 解密工具 ---

_AES_KEY = b"cascade123456789"  # 必須與 full_key.py 相同


def _decrypt_plain(enc: str) -> str:
    if not enc or not enc.startswith("enc:"):
        return enc
    try:
        iv_b64, ct_b64 = enc[4:].split(":", 1)
        iv = base64.b64decode(iv_b64)
        ct = base64.b64decode(ct_b64)
        cipher = AES.new(_AES_KEY, AES.MODE_CBC, iv)
        pt = unpad(cipher.decrypt(ct), AES.block_size).decode("utf-8")
        return pt
    except Exception as e:
        return f"解密失败: {e}"


def add_card(server: str, token: str):
    card = ask("请输入卡密 (留空返回)")
    if not card:
        return
    minutes = ask("有效分钟数", "1440")
    try:
        minutes = int(minutes)
    except ValueError:
        print("分钟数必须是整数")
        return
    resp = requests.post(
        f"{server}/api/card/add",
        headers={"X-API-Token": token, "Content-Type": "application/json"},
        json={"card": card, "minutes": minutes},
        timeout=10,
    )
    pretty(resp.json())


def update_status(server: str, token: str, status: str):
    card = ask("请输入卡密 (留空返回)")
    if not card:
        return
    resp = requests.post(
        f"{server}/api/card/update_status",
        headers={"X-API-Token": token, "Content-Type": "application/json"},
        json={"card": card, "status": status},
        timeout=10,
    )
    pretty(resp.json())


def decrypt_local():
    enc = ask("请输入加密卡密 (留空返回)")
    if not enc:
        return
    if not enc.startswith("enc:"):
        enc = "enc:" + enc
    plain = _decrypt_plain(enc)
    print(f"解密结果: {plain}")


def list_cards_local():
    import time, os
    db_path = PROJECT_ROOT / "key" / "card_db.json"
    if not db_path.exists():
        print(f"找不到 {db_path}")
        return
    with open(db_path, "r", encoding="utf-8") as f:
        db = json.load(f)
    now = time.time()
    print("\n=== 本地卡密列表 ===")
    for k, info in db.items():
        exp = info.get("expire_at", 0)
        if not exp or exp <= now:
            continue
        plain = info.get("plain", "")
        if plain.startswith("enc:"):
            plain = _decrypt_plain(plain)
        valid = "yes" if info.get("valid", False) else "no"
        left = int((exp - now) // 60)
        print(f"卡密: {plain} | 有效: {valid} | 剩余: {left} 分钟 | 哈希: {k[:8]}…")


def list_cards_remote(server: str, token: str):
    import time
    try:
        resp = requests.get(f"{server}/api/card/list", headers={"X-API-Token": token}, timeout=10)
        data = resp.json()
        if not data.get("success"):
            print("API 错误:", data)
            return
        cards = data.get("cards", [])
        if not cards:
            print("远程无卡密。")
            return
        now = time.time()
        print("\n=== 远程卡密列表 ===")
        for item in cards:
            exp = item.get("expire_at", 0)
            if not exp or exp <= now:
                continue
            plain = item.get("card", "")
            valid = "yes" if item.get("valid") else "no"
            left = int((exp - now) // 60)
            print(f"卡密: {plain} | 有效: {valid} | 剩余: {left} 分钟 | 哈希: {item.get('hash_key','')[:8]}…")
    except Exception as e:
        print("请求失败:", e)


def menu_loop():
    server = SERVER_URL
    token = API_TOKEN
    actions = {
        "1": ("新增卡密", lambda: add_card(server, token)),
        "2": ("禁用卡密", lambda: update_status(server, token, "disable")),
        "3": ("启用卡密", lambda: update_status(server, token, "enable")),
        "4": ("删除卡密", lambda: update_status(server, token, "delete")),
        "5": ("解密卡密", decrypt_local),
        "6": ("查看本地卡密", list_cards_local),
        "7": ("查看远程卡密", lambda: list_cards_remote(server, token)),
        "0": ("退出", None),
    }
    while True:
        print("\n菜单:")
        for k, (name, _) in actions.items():
            print(f"  {k}. {name}")
        choice = input("请选择操作: ").strip()
        if choice == "0":
            break
        act = actions.get(choice)
        if not act:
            print("无效选择，请重试。")
            continue
        try:
            act[1]()
        except Exception as e:
            print("执行失败:", e) 