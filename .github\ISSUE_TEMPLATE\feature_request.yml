name: 功能请求
description: 建议一个项目的想法
title: "[Feature Request]: "
labels: ["enhancement", "triage"]
projects: ["octo-org/1", "octo-org/44"]
assignees:
  - octocat
body:
  - type: markdown
    attributes:
      value: |
        感谢您花时间提出功能请求！
  - type: textarea
    id: related-problem
    attributes:
      label: 功能请求是否与某个问题相关？请描述清楚
      description: 请详细描述您的功能请求是否与某个具体问题相关。
      placeholder: 请在此处描述相关问题
    validations:
      required: true
  - type: textarea
    id: describe-features
    attributes:
      label: 描述您希望增加的功能
      description: 请详细描述您希望增加的功能。
      placeholder: 请在此处描述希望增加的功能
    validations:
      required: true
  - type: textarea
    id: alternatives-considered
    attributes:
      label: 描述您考虑过的任何备选解决方案或功能
      description: 请描述您考虑过的任何备选解决方案或功能。
      placeholder: 请在此处描述备选方案
  - type: textarea
    id: additional-context
    attributes:
      label: 其他上下文或截图
      description: 添加关于功能请求的任何其他上下文或截图。
      placeholder: 请在此处添加其他上下文或截图