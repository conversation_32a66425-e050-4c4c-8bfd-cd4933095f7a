import json
import sys
import base64
from typing import Optional

try:
    import requests
    from Crypto.Cipher import AES
    from Crypto.Util.Padding import unpad
except ImportError:
    print("缺少 requests 库，请先执行: pip install requests")
    print("缺少 pycryptodome 库，请先执行: pip install pycryptodome")
    sys.exit(1)


def pretty(data: dict):
    print(json.dumps(data, ensure_ascii=False, indent=2))


def ask(prompt: str, default: Optional[str] = None) -> str:
    s = input(f"{prompt} [{default or ''}]: ").strip()
    return s or (default or "")


# === 解密工具函式 ===

_AES_KEY = b"cascade123456789"  # 與服務端一致的 16-byte 金鑰


def _decrypt_plain(enc: str) -> str:
    """把 enc:IV:ct 解密回明文。若不是 enc: 開頭則直接回傳原字串"""
    if not enc or not enc.startswith("enc:"):
        return enc
    try:
        iv_b64, ct_b64 = enc[4:].split(":", 1)
        iv = base64.b64decode(iv_b64)
        ct = base64.b64decode(ct_b64)
        cipher = AES.new(_AES_KEY, AES.MODE_CBC, iv)
        pt = unpad(cipher.decrypt(ct), AES.block_size).decode("utf-8")
        return pt
    except Exception as e:
        return f"解密失败: {e}"


# === 遠端操作 ===

def add_card(server: str, token: str):
    card = ask("请输入卡密 (留空返回)")
    if not card:
        return
    minutes = ask("有效分钟数", "1440")
    try:
        minutes = int(minutes)
    except ValueError:
        print("分钟数必须是整数")
        return
    resp = requests.post(
        f"{server}/api/card/add",
        headers={"X-API-Token": token, "Content-Type": "application/json"},
        json={"card": card, "minutes": minutes},
        timeout=10,
    )
    pretty(resp.json())


def update_status(server: str, token: str, status: str):
    card = ask("请输入卡密 (留空返回)")
    if not card:
        return
    resp = requests.post(
        f"{server}/api/card/update_status",
        headers={"X-API-Token": token, "Content-Type": "application/json"},
        json={"card": card, "status": status},
        timeout=10,
    )
    pretty(resp.json())


# === 本地功能 ===

def decrypt_local():
    enc = ask("请输入加密卡密 (留空返回)")
    if not enc:
        return
    if not enc.startswith("enc:"):
        enc = "enc:" + enc
    plain = _decrypt_plain(enc)
    print(f"解密结果: {plain}")


def list_cards_local():
    import os
    import time

    db_path = os.path.normpath(os.path.join(os.path.dirname(__file__), "..", "key", "card_db.json"))
    if not os.path.exists(db_path):
        print(f"找不到数据库文件: {db_path}")
        return

    try:
        with open(db_path, "r", encoding="utf-8") as f:
            db = json.load(f)

        now = time.time()
        print("\n=== 本地卡密列表 ===")
        for hash_key, info in db.items():
            expire_at = info.get("expire_at", 0)
            if not expire_at or expire_at <= now:
                continue  # 過期

            enc_plain = info.get("plain", "")
            plain = _decrypt_plain(enc_plain) if enc_plain.startswith("enc:") else enc_plain
            valid = "yes" if info.get("valid", False) else "no"
            left = int((expire_at - now) // 60)
            print(f"卡密: {plain} | 有效: {valid} | 剩餘: {left} 分鐘 | 哈希: {hash_key[:8]}…")
    except Exception as e:
        print(f"读取或解析数据库失败: {e}")


# === 遠端列表 ===

def list_cards_remote(server: str, token: str):
    import time
    try:
        resp = requests.get(
            f"{server}/api/card/list",
            headers={"X-API-Token": token},
            timeout=10,
        )
        data = resp.json()
        if not data.get("success"):
            print(f"API 返回錯誤: {data}")
            return

        cards = data.get("cards", [])
        if not cards:
            print("遠端無卡密。")
            return

        now = time.time()
        print("\n=== 遠端卡密列表 ===")
        for item in cards:
            expire_at = item.get("expire_at", 0)
            if not expire_at or expire_at <= now:
                continue
            plain = item.get("card", "")
            valid = "yes" if item.get("valid") else "no"
            left = int((expire_at - now) // 60)
            hash_key = item.get("hash_key", "")
            print(f"卡密: {plain} | 有效: {valid} | 剩餘: {left} 分鐘 | 哈希: {hash_key[:8]}…")
    except Exception as e:
        print(f"请求远端卡密失败: {e}")


# === 主程序 ===

DEFAULT_SERVER = "http://************:5000"
DEFAULT_TOKEN = "3f8c1a9e4b9d63a01e6d2c14c956aa7b"

def main():
    print("=== 远程卡密管理 (电脑1) ===")

    token = ask("API_TOKEN", DEFAULT_TOKEN).strip()
    if not token:
        print("未输入 API_TOKEN，程序退出。")
        return

    server = ask("服务器地址", DEFAULT_SERVER)
    if not server.startswith(("http://", "https://")):
        server = "http://" + server
    server = server.rstrip("/")

    actions = {
        "1": ("新增卡密", lambda: add_card(server, token)),
        "2": ("禁用卡密", lambda: update_status(server, token, "disable")),
        "3": ("启用卡密", lambda: update_status(server, token, "enable")),
        "4": ("删除卡密", lambda: update_status(server, token, "delete")),
        "5": ("解密卡密", decrypt_local),
        "6": ("查看本地卡密", list_cards_local),
        "7": ("查看網站卡密", lambda: list_cards_remote(server, token)),
        "0": ("退出", None),
    }

    while True:
        print("\n菜单:")
        for k, (name, _) in actions.items():
            print(f"  {k}. {name}")
        choice = input("请选择操作: ").strip()
        if choice == "0":
            print("再见！")
            break
        action = actions.get(choice)
        if not action:
            print("无效选择，请重试。")
            continue
        try:
            action[1]()
        except requests.RequestException as e:
            print(f"请求失败: {e}")
        except Exception as e:
            print(f"发生错误: {e}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n已取消") 