# HohoAI 自动视频+YOLO功能说明

## 🎉 新功能：全自动运行

现在使用 `python start_service.py --skip-auth` 会自动启动：

1. **✅ 卡密验证** - 输入卡密进行验证
2. **✅ 后台服务** - 启动无头服务
3. **✅ 配置文件** - 自动打开JSON配置
4. **✅ 视频捕获** - 自动开始屏幕捕获
5. **✅ YOLO检测** - 自动启用目标检测
6. **✅ 瞄准功能** - 自动激活瞄准逻辑

## 🚀 一键启动

```bash
python start_service.py --skip-auth
```

## 📋 完整启动流程

```
🔧 启动调试模式...
请输入卡密 (第1/3次): [输入卡密]
✅ 卡密验证通过！
🚀 启动服务并打开配置文件...
📡 正在启动后台服务...
✅ 后台服务已启动
📝 正在打开配置文件...
✅ 已自动打开配置文件: hohoai_config.json

============================================================
🎯 HohoAI 调试模式已就绪
============================================================
📡 后台服务: 运行中
📝 配置文件: 已打开
🔄 实时更新: 启用
📹 视频捕获: 自动启动
🤖 YOLO检测: 自动启用
🎯 瞄准功能: 已激活

💡 使用说明:
   - 编辑配置文件中的参数
   - 保存文件后自动应用到服务
   - 视频和YOLO会自动运行
   - 按 Ctrl+C 停止所有服务
============================================================

📊 状态更新 #1 - 14:05:30
   📡 服务: 运行中 | 📹 视频: 捕获中 | 🤖 YOLO: 检测中
```

## 🎮 自动功能详解

### 📹 自动视频捕获
- **捕获区域**: 屏幕中心 200x200 像素
- **帧率**: 20 FPS
- **格式**: RGB格式，实时处理
- **状态**: 启动后自动开始

### 🤖 自动YOLO检测
- **模型加载**: 自动加载配置中的模型文件
- **预热**: 启动时自动预热模型
- **检测**: 实时检测屏幕中的目标
- **日志**: 每100帧记录一次状态

### 🎯 自动瞄准逻辑
- **目标识别**: 自动识别最近的目标
- **距离计算**: 计算目标到屏幕中心的距离
- **范围判断**: 根据配置的瞄准范围判断是否瞄准
- **偏移计算**: 计算需要的鼠标移动偏移

## ⚙️ 配置文件新增设置

```json
{
  "aimbot_settings": {
    "enabled": true,           // 自动启用瞄准功能
    "aim_speed_x": 0.2,       // X轴瞄准速度
    "aim_range": 100,         // 瞄准范围
    "yolo_confidence": 0.5,   // YOLO检测置信度
    "target_class": "ALL"     // 检测目标类别
  },
  "model_settings": {
    "model_file": "yolov8n.pt",      // YOLO模型文件
    "use_gpu_safe_yolo": true,       // 使用GPU安全模式
    "device": "cuda:0"               // 使用的设备
  },
  "video_settings": {
    "auto_start_video": true,        // 自动启动视频捕获
    "auto_enable_yolo": true,        // 自动启用YOLO检测
    "capture_width": 200,            // 捕获宽度
    "capture_height": 200,           // 捕获高度
    "frame_rate": 20                 // 帧率
  }
}
```

## 📊 实时状态监控

服务运行时会显示：

### 日志信息
```
[INFO] Graphics Service: Initializing YOLO model: yolov8n.pt
[INFO] Graphics Service: YOLO model initialized and warmed up successfully
[INFO] Graphics Service: Starting video capture...
[INFO] Graphics Service: Video capture started successfully
[INFO] Graphics Service: Auto-enabled YOLO detection based on configuration
[INFO] Graphics Service: Video capture: 100 frames processed
[INFO] Graphics Service: YOLO detected 2 targets, closest at distance 45.3
[INFO] Graphics Service: Target in range: offset(12.5, -8.2)
```

### 状态更新
每10秒显示一次运行状态：
```
📊 状态更新 #1 - 14:05:30
   📡 服务: 运行中 | 📹 视频: 捕获中 | 🤖 YOLO: 检测中
```

## 🔧 参数实时调整

编辑配置文件中的参数，保存后立即生效：

### 调整检测精度
```json
"yolo_confidence": 0.6  // 提高到0.6，减少误检
```

### 调整瞄准范围
```json
"aim_range": 150  // 扩大瞄准范围到150像素
```

### 更换模型文件
```json
"model_file": "yolov8s.pt"  // 使用更精确的模型
```

### 调整捕获区域
```json
"capture_width": 300,   // 扩大捕获区域
"capture_height": 300
```

## 🛑 停止服务

按 `Ctrl+C` 停止所有功能：
```
🛑 正在停止服务...
[INFO] Graphics Service: Stopping video capture...
[INFO] Graphics Service: Video capture stopped
[INFO] Graphics Service: YOLO detection disabled
[INFO] Graphics Service: Background service stopped
✅ 服务已停止
```

## 🎯 功能优势

- ✅ **完全自动化** - 一键启动所有功能
- ✅ **实时检测** - 20FPS视频捕获+YOLO检测
- ✅ **参数热更新** - 修改配置立即生效
- ✅ **完全隐蔽** - 无GUI界面，伪装成系统服务
- ✅ **智能瞄准** - 自动识别最近目标并计算偏移
- ✅ **状态监控** - 实时显示运行状态

## 🔍 故障排除

### YOLO模型加载失败
- 检查模型文件是否存在
- 确认YOLO相关依赖已安装
- 查看日志中的错误信息

### 视频捕获异常
- 检查屏幕分辨率设置
- 确认mss模块正常工作
- 查看捕获区域配置

### 检测效果不佳
- 调整yolo_confidence参数
- 更换更精确的模型文件
- 调整target_class设置

现在您的HohoAI具备了完全自动化的视频捕获和YOLO检测功能！🎮🎯
