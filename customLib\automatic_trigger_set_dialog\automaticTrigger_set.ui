<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>371</width>
    <height>250</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <widget class="QSlider" name="autoTiggerRangeSlider">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>165</y>
     <width>181</width>
     <height>20</height>
    </rect>
   </property>
   <property name="orientation">
    <enum>Qt::Orientation::Horizontal</enum>
   </property>
  </widget>
  <widget class="QRadioButton" name="radioButton">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>40</y>
     <width>101</width>
     <height>21</height>
    </rect>
   </property>
   <property name="tabletTracking">
    <bool>false</bool>
   </property>
   <property name="acceptDrops">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true">font: 12pt &quot;Microsoft YaHei&quot;;</string>
   </property>
   <property name="text">
    <string>持续生效</string>
   </property>
   <property name="checked">
    <bool>false</bool>
   </property>
   <attribute name="buttonGroup">
    <string notr="true">buttonGroup</string>
   </attribute>
  </widget>
  <widget class="QLCDNumber" name="autoTiggerRangeNumber">
   <property name="geometry">
    <rect>
     <x>285</x>
     <y>165</y>
     <width>64</width>
     <height>23</height>
    </rect>
   </property>
   <property name="frameShape">
    <enum>QFrame::Shape::Box</enum>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Shadow::Plain</enum>
   </property>
   <property name="segmentStyle">
    <enum>QLCDNumber::SegmentStyle::Flat</enum>
   </property>
  </widget>
  <widget class="QLabel" name="triggerFormLabel">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>120</y>
     <width>71</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
   </property>
   <property name="text">
    <string>触发形式:</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>0</y>
     <width>101</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="text">
    <string>自动扳机配置</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="QComboBox" name="triggerFormcomboBox">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>120</y>
     <width>131</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>10</pointsize>
    </font>
   </property>
   <item>
    <property name="text">
     <string>连点</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>单点</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>长按（未开发）</string>
    </property>
   </item>
  </widget>
  <widget class="QRadioButton" name="radioButton_2">
   <property name="geometry">
    <rect>
     <x>170</x>
     <y>40</y>
     <width>131</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="text">
    <string>按下自瞄生效</string>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <attribute name="buttonGroup">
    <string notr="true">buttonGroup</string>
   </attribute>
  </widget>
  <widget class="QLabel" name="aimingRangeLabel_5">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>160</y>
     <width>71</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
   </property>
   <property name="text">
    <string>范围比例:</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="triggerPositionLabel">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>80</y>
     <width>71</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
   </property>
   <property name="text">
    <string>触发位置:</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
   </property>
  </widget>
  <widget class="QComboBox" name="triggerPositioncomboBox">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>80</y>
     <width>131</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>10</pointsize>
    </font>
   </property>
   <item>
    <property name="text">
     <string>使用瞄准位置</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>自定义（未启用）</string>
    </property>
   </item>
  </widget>
  <widget class="QCheckBox" name="wasdDetectionCheckBox">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>200</y>
     <width>200</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="text">
    <string>WASD检测（移动时关闭自动扳机）</string>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
 <buttongroups>
  <buttongroup name="buttonGroup"/>
 </buttongroups>
</ui>
