import torch
import torch.onnx
import sys
import os

def convert_pt_to_onnx(pt_file_path, onnx_file_path, input_shape=(1, 3, 640, 640)):
    """
    将PyTorch模型转换为ONNX格式
    
    Args:
        pt_file_path: .pt文件路径
        onnx_file_path: 输出的.onnx文件路径
        input_shape: 输入张量的形状 (batch_size, channels, height, width)
    """
    
    try:
        # 检查文件是否存在
        if not os.path.exists(pt_file_path):
            print(f"错误: 找不到文件 {pt_file_path}")
            return False
        
        print(f"正在加载模型: {pt_file_path}")
        
        # 尝试不同的加载方式
        try:
            # 方式1: 直接加载模型
            model = torch.load(pt_file_path, map_location='cpu')
            
            # 如果加载的是字典，尝试获取模型
            if isinstance(model, dict):
                if 'model' in model:
                    model = model['model']
                elif 'state_dict' in model:
                    print("检测到state_dict格式，需要模型架构信息")
                    print("请提供模型架构或使用完整的模型文件")
                    return False
            
        except Exception as e:
            print(f"加载模型时出错: {e}")
            return False
        
        # 设置为评估模式
        model.eval()
        
        print(f"模型类型: {type(model)}")
        print(f"输入形状: {input_shape}")
        
        # 创建示例输入
        dummy_input = torch.randn(*input_shape)
        
        print("开始转换为ONNX...")
        
        # 转换为ONNX
        torch.onnx.export(
            model,                          # 模型
            dummy_input,                    # 示例输入
            onnx_file_path,                # 输出文件路径
            export_params=True,             # 导出参数
            opset_version=11,               # ONNX操作集版本
            do_constant_folding=True,       # 常量折叠优化
            input_names=['input'],          # 输入名称
            output_names=['output'],        # 输出名称
            dynamic_axes={                  # 动态轴（可选）
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            }
        )
        
        print(f"转换成功! ONNX模型已保存到: {onnx_file_path}")
        
        # 验证ONNX模型
        import onnx
        onnx_model = onnx.load(onnx_file_path)
        onnx.checker.check_model(onnx_model)
        print("ONNX模型验证通过!")
        
        return True
        
    except Exception as e:
        print(f"转换过程中出错: {e}")
        return False

def main():
    # 默认参数
    pt_file = "best.pt"
    onnx_file = "best.onnx"
    
    # 常见的输入形状，你可能需要根据你的模型调整
    input_shapes = {
        "yolo": (1, 3, 640, 640),      # YOLO模型
        "resnet": (1, 3, 224, 224),    # ResNet等分类模型
        "custom": (1, 3, 416, 416),    # 自定义尺寸
    }
    
    print("PyTorch to ONNX 转换工具")
    print("=" * 40)
    
    # 检查best.pt是否存在
    if not os.path.exists(pt_file):
        print(f"错误: 找不到 {pt_file} 文件")
        print("请确保best.pt文件在当前目录中")
        return
    
    print("检测到的可能模型类型:")
    print("1. YOLO模型 (640x640)")
    print("2. 分类模型 (224x224)")
    print("3. 自定义尺寸 (416x416)")
    print("4. 手动输入尺寸")
    
    choice = input("请选择模型类型 (1-4): ").strip()
    
    if choice == "1":
        input_shape = input_shapes["yolo"]
    elif choice == "2":
        input_shape = input_shapes["resnet"]
    elif choice == "3":
        input_shape = input_shapes["custom"]
    elif choice == "4":
        try:
            print("请输入输入张量形状 (格式: batch_size,channels,height,width)")
            shape_str = input("例如: 1,3,640,640: ").strip()
            input_shape = tuple(map(int, shape_str.split(',')))
        except:
            print("输入格式错误，使用默认形状 (1,3,640,640)")
            input_shape = (1, 3, 640, 640)
    else:
        print("无效选择，使用默认YOLO形状")
        input_shape = input_shapes["yolo"]
    
    # 执行转换
    success = convert_pt_to_onnx(pt_file, onnx_file, input_shape)
    
    if success:
        print("\n转换完成!")
        print(f"输入文件: {pt_file}")
        print(f"输出文件: {onnx_file}")
        print(f"输入形状: {input_shape}")
    else:
        print("\n转换失败，请检查错误信息")

if __name__ == "__main__":
    main()
