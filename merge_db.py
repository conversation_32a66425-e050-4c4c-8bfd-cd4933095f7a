#!/usr/bin/env python3
"""merge_db.py - 將 key v2 產生的 card_db.json 與本地 key/card_db.json 合併
使用方法：
1. 將 key v2 伺服器的 card_db.json 複製到專案根目錄並命名為 card_db_remote.json
2. 執行: python merge_db.py
3. 合併完成後即可直接執行 python hohoai.py
"""
import json, os, shutil
from datetime import datetime

LOCAL_PATH  = os.path.join('key', 'card_db.json')
REMOTE_PATH = 'card_db_remote.json'
BACKUP_DIR  = 'db_backup'

def load_json(path):
    try:
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}
    except Exception as e:
        print(f'讀取 {path} 失敗: {e}')
        return {}

def backup_local_db():
    if not os.path.exists(LOCAL_PATH):
        return
    os.makedirs(BACKUP_DIR, exist_ok=True)
    ts = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = os.path.join(BACKUP_DIR, f'card_db_{ts}.json')
    shutil.copy2(LOCAL_PATH, backup_path)
    print(f'已備份本地資料庫 -> {backup_path}')

def merge_db():
    remote_db = load_json(REMOTE_PATH)
    if not remote_db:
        print(f'找不到 {REMOTE_PATH} 或內容為空，請先把 key v2 的 card_db.json 複製過來並改名。')
        return
    local_db = load_json(LOCAL_PATH)
    added = overwritten = 0
    for k, v in remote_db.items():
        if k in local_db:
            overwritten += 1
        else:
            added += 1
        local_db[k] = v
    os.makedirs(os.path.dirname(LOCAL_PATH), exist_ok=True)
    with open(LOCAL_PATH, 'w', encoding='utf-8') as f:
        json.dump(local_db, f, ensure_ascii=False, indent=2)
    print(f'合併完成！新增 {added} 條，覆蓋 {overwritten} 條。')
    print(f'最終本地資料數量：{len(local_db)}')

if __name__ == '__main__':
    backup_local_db()
    merge_db() 