#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编译脚本 - 将Python脚本编译为exe文件
完全隐藏Python进程
"""

import os
import sys
import subprocess
import shutil

def install_compiler():
    """安装编译器"""
    print("📦 安装编译器...")
    
    try:
        # 安装PyInstaller
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller 安装成功")
        
        # 尝试安装Nuitka（可选）
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "nuitka"], check=True)
            print("✅ Nuitka 安装成功")
            return True
        except:
            print("⚠️ Nuitka 安装失败，将使用PyInstaller")
            return False
            
    except Exception as e:
        print(f"❌ 编译器安装失败: {e}")
        return False

def compile_with_pyinstaller():
    """使用PyInstaller编译"""
    print("🔧 使用PyInstaller编译...")
    
    # 编译选项
    compile_options = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                    # 单文件
        "--noconsole",                  # 无控制台窗口
        "--name", "nvdisplay",          # 输出文件名
        "--icon", "NONE",               # 无图标
        "--distpath", "dist",           # 输出目录
        "--workpath", "build",          # 工作目录
        "--specpath", ".",              # spec文件目录
        "headless_service.py"           # 源文件
    ]
    
    try:
        subprocess.run(compile_options, check=True)
        print("✅ PyInstaller编译成功")
        return True
    except Exception as e:
        print(f"❌ PyInstaller编译失败: {e}")
        return False

def compile_with_nuitka():
    """使用Nuitka编译"""
    print("🔧 使用Nuitka编译...")
    
    compile_options = [
        sys.executable, "-m", "nuitka",
        "--onefile",                           # 单文件
        "--windows-disable-console",          # 禁用控制台
        "--output-filename=nvdisplay.exe",    # 输出文件名
        "--remove-output",                     # 移除临时文件
        "headless_service.py"                  # 源文件
    ]
    
    try:
        subprocess.run(compile_options, check=True)
        print("✅ Nuitka编译成功")
        return True
    except Exception as e:
        print(f"❌ Nuitka编译失败: {e}")
        return False

def create_fake_files():
    """创建伪装文件"""
    print("🎭 创建伪装文件...")
    
    fake_files = {
        "nvdisplay.ini": "[NVIDIA Display Container]\nVersion=531.79\nStatus=Active\n",
        "gpu_driver.log": "NVIDIA GPU Driver initialized successfully\n",
        "display_adapter.dat": "Display Adapter: NVIDIA GeForce RTX\n"
    }
    
    for filename, content in fake_files.items():
        try:
            with open(filename, 'w') as f:
                f.write(content)
        except:
            pass
    
    print("✅ 伪装文件创建完成")

def main():
    """主函数"""
    print("🛡️ HohoAI 编译器")
    print("将Python脚本编译为exe文件，完全隐藏进程")
    
    while True:
        print("\n" + "="*50)
        print("选择编译方式:")
        print("1. 使用PyInstaller编译（推荐）")
        print("2. 使用Nuitka编译（更好性能）")
        print("3. 安装编译器")
        print("4. 创建伪装文件")
        print("0. 退出")
        
        choice = input("\n请选择 (0-4): ").strip()
        
        if choice == "0":
            break
        elif choice == "1":
            if compile_with_pyinstaller():
                create_fake_files()
                print("\n🎉 编译完成！")
                print("📁 编译后的文件: dist/nvdisplay.exe")
                print("💡 建议重命名为系统进程名，如:")
                print("   - dwm.exe")
                print("   - audiodg.exe") 
                print("   - winlogon.exe")
        elif choice == "2":
            if compile_with_nuitka():
                create_fake_files()
                print("\n🎉 编译完成！")
                print("📁 编译后的文件: nvdisplay.exe")
        elif choice == "3":
            install_compiler()
        elif choice == "4":
            create_fake_files()
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
