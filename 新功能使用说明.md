# HohoAI 新功能使用说明

## 🎯 新功能：一键启动 + 自动配置

现在使用 `python start_service.py --skip-auth` 会：

1. **✅ 验证卡密** - 输入卡密进行验证
2. **✅ 启动服务** - 后台启动无头服务
3. **✅ 自动打开配置** - 自动打开 JSON 配置文件
4. **✅ 实时监控** - 配置文件修改后自动应用

## 🚀 使用方法

### 方法1：命令行启动
```bash
python start_service.py --skip-auth
```

### 方法2：双击批处理文件
```
双击 quick_start.bat
```

## 📝 完整流程演示

```
🔧 启动调试模式...
请输入卡密 (第1/3次): [输入您的卡密]
🔍 正在验证卡密...
✅ 卡密验证通过！
🚀 卡密验证成功，启动服务并打开配置文件...
📡 正在启动后台服务...
✅ 后台服务已启动
📝 正在打开配置文件...
✅ 已自动打开配置文件: hohoai_config.json

============================================================
🎯 HohoAI 调试模式已就绪
============================================================
📡 后台服务: 运行中
📝 配置文件: 已打开
🔄 实时更新: 启用

💡 使用说明:
   - 编辑配置文件中的参数
   - 保存文件后自动应用到服务
   - 按 Ctrl+C 停止服务
============================================================
```

## ⚙️ 配置文件参数说明

打开的 `hohoai_config.json` 文件包含：

### 🎯 瞄准设置
```json
"aimbot_settings": {
  "enabled": true,           // 启用自瞄
  "aim_speed_x": 0.2,       // X轴速度
  "aim_speed_y": 0.0,       // Y轴速度
  "aim_range": 100,         // 瞄准范围
  "offset_centerx": 0,      // X轴偏移
  "offset_centery": 0.3,    // Y轴偏移
  "yolo_confidence": 0.5    // 检测置信度
}
```

### ⚙️ 高级设置
```json
"advanced_settings": {
  "dynamic_aim_range_enabled": true,  // 动态瞄准
  "smooth_aiming": false,             // 平滑瞄准
  "recoil_suppression_switch": false, // 压枪辅助
  "recoil_strength": 50               // 压枪强度
}
```

## 🔄 实时调参步骤

1. **修改参数** - 在打开的JSON文件中修改数值
2. **保存文件** - Ctrl+S 保存
3. **自动应用** - 后台服务自动检测并应用新设置
4. **查看日志** - 控制台会显示配置更新信息

## 💡 使用技巧

### 快速调整常用参数
```json
// 提高瞄准速度
"aim_speed_x": 0.5,

// 扩大瞄准范围  
"aim_range": 150,

// 启用平滑瞄准
"smooth_aiming": true,

// 调整检测精度
"yolo_confidence": 0.6
```

### 保存配置备份
```bash
copy hohoai_config.json hohoai_config_backup.json
```

## 🛑 停止服务

在控制台按 `Ctrl+C` 即可停止服务：
```
🛑 正在停止服务...
✅ 服务已停止
```

## 🎮 完整工作流程

1. **启动** - `python start_service.py --skip-auth`
2. **验证** - 输入卡密
3. **编辑** - 在自动打开的配置文件中调整参数
4. **保存** - Ctrl+S 保存文件
5. **测试** - 参数立即生效
6. **停止** - Ctrl+C 停止服务

## 🔍 故障排除

### 配置文件没有自动打开
- 检查是否有默认的JSON编辑器
- 手动打开 `hohoai_config.json`

### 参数修改不生效
- 确认文件已保存
- 检查JSON格式是否正确
- 查看控制台是否有错误信息

### 卡密验证失败
- 检查网络连接
- 确认卡密输入正确
- 联系管理员

## 🎯 优势

- ✅ **一键启动** - 单个命令完成所有操作
- ✅ **自动配置** - 无需手动打开配置文件
- ✅ **实时更新** - 修改立即生效
- ✅ **完全隐蔽** - 后台运行，无GUI界面
- ✅ **安全验证** - 保持卡密验证机制

现在您可以更方便地使用 HohoAI 了！🚀
