#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
hohoai.py 加密腳本
使用 PyArmor 進行代碼混淆和加密
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyarmor():
    """安裝 PyArmor"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyarmor"], check=True)
        print("✓ PyArmor 安裝成功")
        return True
    except subprocess.CalledProcessError:
        print("✗ PyArmor 安裝失敗")
        return False

def encrypt_with_pyarmor():
    """使用 PyArmor 加密 hohoai.py"""
    try:
        # 創建加密後的目錄
        output_dir = "encrypted"
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)
        os.makedirs(output_dir)
        
        # 執行 PyArmor 加密
        cmd = [
            sys.executable, "-m", "pyarmor.cli.core", "obfuscate",
            "--recursive",
            "--output", output_dir,
            "hohoai.py"
        ]
        
        print("正在加密 hohoai.py...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 加密成功！")
            print(f"加密後的文件位置: {output_dir}/")
            
            # 複製必要的依賴文件
            copy_dependencies(output_dir)
            
            return True
        else:
            print("✗ 加密失敗")
            print("錯誤信息:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 加密過程中發生錯誤: {e}")
        return False

def copy_dependencies(output_dir):
    """複製必要的依賴文件和目錄"""
    dependencies = [
        "Module/",
        "customLib/",
        "UI/",
        "Utils/",
        "Model/",
        "DLLs/",
        "images/",
        "key/",
        "templates/"
    ]
    
    print("正在複製依賴文件...")
    for dep in dependencies:
        if os.path.exists(dep):
            dest = os.path.join(output_dir, dep)
            if os.path.isdir(dep):
                shutil.copytree(dep, dest, dirs_exist_ok=True)
            else:
                shutil.copy2(dep, dest)
            print(f"✓ 複製 {dep}")
    
    # 複製其他必要文件
    other_files = [
        "hohoai.spec",
        "pyproject.toml",
        "LICENSE",
        "yolov8n.pt"
    ]
    
    for file in other_files:
        if os.path.exists(file):
            shutil.copy2(file, output_dir)
            print(f"✓ 複製 {file}")

def create_launcher(output_dir):
    """創建啟動器腳本"""
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加密後的 hohoai 啟動器
"""

import os
import sys

# 添加當前目錄到 Python 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 導入並運行加密後的主程序
try:
    from hohoai import main
    if __name__ == "__main__":
        main()
except ImportError as e:
    print(f"導入錯誤: {e}")
    print("請確保所有依賴文件都已正確複製")
except Exception as e:
    print(f"運行錯誤: {e}")
'''
    
    launcher_path = os.path.join(output_dir, "run_hohoai.py")
    with open(launcher_path, "w", encoding="utf-8") as f:
        f.write(launcher_content)
    
    print(f"✓ 創建啟動器: {launcher_path}")

def main():
    """主函數"""
    print("=== hohoai.py 加密工具 ===")
    print()
    
    # 檢查原文件是否存在
    if not os.path.exists("hohoai.py"):
        print("✗ 錯誤: 找不到 hohoai.py 文件")
        return
    
    # 安裝 PyArmor
    if not install_pyarmor():
        return
    
    # 加密文件
    if encrypt_with_pyarmor():
        # 創建啟動器
        create_launcher("encrypted")
        
        print()
        print("=== 加密完成 ===")
        print("加密後的文件在 'encrypted' 目錄中")
        print("使用方法:")
        print("1. 進入 encrypted 目錄")
        print("2. 運行 python run_hohoai.py")
        print()
        print("注意: 原始 hohoai.py 文件保持不變")
    else:
        print("加密失敗，請檢查錯誤信息")

if __name__ == "__main__":
    main() 