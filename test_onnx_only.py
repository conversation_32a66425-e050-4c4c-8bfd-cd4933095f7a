import onnxruntime as ort
import numpy as np

def test_onnx_basic():
    """测试基本ONNX功能"""
    
    print("ONNX Runtime版本:", ort.__version__)
    print("可用的执行提供者:", ort.get_available_providers())
    
    # 测试CPU提供者（跳过，因为需要有效模型文件）
    print("\n✅ CPU提供者在可用列表中")
    
    # 测试CUDA提供者（不创建会话，只检查可用性）
    if 'CUDAExecutionProvider' in ort.get_available_providers():
        print("✅ CUDA提供者在列表中")
        
        # 尝试获取CUDA提供者信息
        try:
            providers = ort.get_available_providers()
            cuda_index = providers.index('CUDAExecutionProvider')
            print(f"✅ CUDA提供者索引: {cuda_index}")
        except Exception as e:
            print(f"❌ 获取CUDA提供者信息失败: {e}")
    else:
        print("❌ CUDA提供者不在可用列表中")

def test_simple_inference():
    """测试简单推理"""
    
    try:
        # 创建一个最简单的CPU会话
        import onnx
        from onnx import helper, TensorProto
        
        # 创建一个简单的ONNX模型 (identity操作)
        input_tensor = helper.make_tensor_value_info('input', TensorProto.FLOAT, [1, 1])
        output_tensor = helper.make_tensor_value_info('output', TensorProto.FLOAT, [1, 1])

        identity_node = helper.make_node('Identity', ['input'], ['output'])

        graph = helper.make_graph([identity_node], 'simple_graph', [input_tensor], [output_tensor])
        model = helper.make_model(graph)

        # 设置兼容的IR版本
        model.ir_version = 8  # 使用兼容的IR版本
        
        # 保存模型
        onnx.save(model, 'simple_model.onnx')
        
        # 测试CPU推理
        session = ort.InferenceSession('simple_model.onnx', providers=['CPUExecutionProvider'])
        input_data = np.array([[1.0]], dtype=np.float32)
        result = session.run(None, {'input': input_data})
        
        print("✅ 简单CPU推理测试成功")
        print(f"输入: {input_data}, 输出: {result[0]}")
        
        # 清理
        import os
        if os.path.exists('simple_model.onnx'):
            os.remove('simple_model.onnx')
            
        return True
        
    except Exception as e:
        print(f"❌ 简单推理测试失败: {e}")
        return False

if __name__ == "__main__":
    print("ONNX基础功能测试")
    print("=" * 40)
    
    test_onnx_basic()
    print("\n" + "=" * 40)
    test_simple_inference()
