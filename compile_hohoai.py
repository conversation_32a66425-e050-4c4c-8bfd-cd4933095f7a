#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
hohoai.py Cython 編譯腳本
將 Python 代碼編譯為 C 擴展，提高執行效率並增加保護
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_cython():
    """安裝 Cython"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "cython"], check=True)
        print("✓ Cython 安裝成功")
        return True
    except subprocess.CalledProcessError:
        print("✗ Cython 安裝失敗")
        return False

def create_setup_py():
    """創建 setup.py 文件"""
    setup_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
setup.py for hohoai Cython compilation
"""

from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy

# 定義 Cython 擴展
extensions = [
    Extension(
        "hohoai_compiled",
        ["hohoai.pyx"],
        include_dirs=[numpy.get_include()],
        define_macros=[("NPY_NO_DEPRECATED_API", "NPY_1_7_API_VERSION")]
    )
]

setup(
    name="hohoai_compiled",
    ext_modules=cythonize(extensions, compiler_directives={
        'language_level': 3,
        'boundscheck': False,
        'wraparound': False,
        'initializedcheck': False,
        'nonecheck': False,
        'cdivision': True,
    }),
    zip_safe=False,
)
'''
    
    with open("setup.py", "w", encoding="utf-8") as f:
        f.write(setup_content)
    
    print("✓ 創建 setup.py")

def create_pyx_file():
    """創建 .pyx 文件"""
    # 讀取原始 hohoai.py
    with open("hohoai.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 寫入 .pyx 文件
    with open("hohoai.pyx", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✓ 創建 hohoai.pyx")

def compile_with_cython():
    """使用 Cython 編譯"""
    try:
        print("正在編譯 hohoai.py...")
        
        # 執行編譯
        cmd = [sys.executable, "setup.py", "build_ext", "--inplace"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 編譯成功！")
            return True
        else:
            print("✗ 編譯失敗")
            print("錯誤信息:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 編譯過程中發生錯誤: {e}")
        return False

def create_compiled_launcher():
    """創建編譯後的啟動器"""
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
編譯後的 hohoai 啟動器
"""

import os
import sys

# 添加當前目錄到 Python 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 導入並運行編譯後的主程序
try:
    import hohoai_compiled
    if __name__ == "__main__":
        hohoai_compiled.main()
except ImportError as e:
    print(f"導入錯誤: {e}")
    print("請確保編譯成功且所有依賴文件都已正確複製")
except Exception as e:
    print(f"運行錯誤: {e}")
'''
    
    with open("run_compiled_hohoai.py", "w", encoding="utf-8") as f:
        f.write(launcher_content)
    
    print("✓ 創建編譯後啟動器: run_compiled_hohoai.py")

def cleanup_temp_files():
    """清理臨時文件"""
    temp_files = [
        "setup.py",
        "hohoai.pyx",
        "build/",
        "hohoai_compiled.c",
        "hohoai_compiled.cpython-*.pyd",
        "hohoai_compiled.cpython-*.so"
    ]
    
    for file in temp_files:
        if os.path.exists(file):
            if os.path.isdir(file):
                shutil.rmtree(file)
            else:
                os.remove(file)
            print(f"✓ 清理 {file}")

def main():
    """主函數"""
    print("=== hohoai.py Cython 編譯工具 ===")
    print()
    
    # 檢查原文件是否存在
    if not os.path.exists("hohoai.py"):
        print("✗ 錯誤: 找不到 hohoai.py 文件")
        return
    
    # 安裝 Cython
    if not install_cython():
        return
    
    # 創建必要的文件
    create_setup_py()
    create_pyx_file()
    
    # 編譯文件
    if compile_with_cython():
        # 創建啟動器
        create_compiled_launcher()
        
        print()
        print("=== 編譯完成 ===")
        print("編譯後的文件:")
        print("- hohoai_compiled.pyd (Windows) 或 .so (Linux/Mac)")
        print("- run_compiled_hohoai.py (啟動器)")
        print()
        print("使用方法:")
        print("python run_compiled_hohoai.py")
        print()
        print("注意: 原始 hohoai.py 文件保持不變")
        
        # 清理臨時文件
        cleanup_temp_files()
    else:
        print("編譯失敗，請檢查錯誤信息")

if __name__ == "__main__":
    main() 