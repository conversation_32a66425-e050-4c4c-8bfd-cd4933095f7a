
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed, optional)
missing module named 'collections.abc' - imported by traceback (top-level), typing (top-level), inspect (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), http.client (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named pynput - imported by Module.keyboard (top-level)
missing module named colorama - imported by Module.logger (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named mouse - imported by Module.control (top-level)
missing module named multiprocessing.Event - imported by multiprocessing (top-level), C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level)
missing module named multiprocessing.Queue - imported by multiprocessing (top-level), C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level)
missing module named multiprocessing.Pipe - imported by multiprocessing (top-level), C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level)
missing module named 'PyQt6.QtWidgets' - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level), customLib.animated_status (top-level), customLib.automatic_trigger_set_dialog (top-level)
missing module named 'PyQt6.QtGui' - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level), customLib.automatic_trigger_set_dialog (top-level)
missing module named 'PyQt6.QtCore' - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level), customLib.animated_status (top-level)
missing module named PyQt6 - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level), customLib.automatic_trigger_set_dialog (top-level)
missing module named ultralytics - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level)
missing module named win32con - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level), Module.control (top-level)
missing module named win32api - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level), Module.control (top-level)
missing module named pyautogui - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level)
missing module named numpy - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level)
missing module named mss - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level)
missing module named cv2 - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level)
missing module named fcntl - imported by subprocess (optional)
missing module named requests - imported by C:\Users\<USER>\Desktop\v3 - 副本\hohoai.py (top-level), Utils.revision (top-level)
