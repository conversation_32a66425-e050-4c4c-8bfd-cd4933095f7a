#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版本编译器
创建带控制台输出的exe文件，便于调试
"""

import os
import sys
import subprocess

def compile_debug_version():
    """编译调试版本（带控制台）"""
    print("🔧 编译调试版本（带控制台输出）...")
    
    compile_options = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                    # 单文件
        "--console",                    # 保留控制台窗口
        "--name", "nvdisplay_debug",    # 输出文件名
        "--distpath", "dist",           # 输出目录
        "--workpath", "build_debug",    # 工作目录
        "--specpath", ".",              # spec文件目录
        "headless_service.py"           # 源文件
    ]
    
    try:
        subprocess.run(compile_options, check=True)
        print("✅ 调试版本编译成功")
        print("📁 文件位置: dist/nvdisplay_debug.exe")
        print("💡 这个版本会显示控制台，可以看到错误信息")
        return True
    except Exception as e:
        print(f"❌ 编译失败: {e}")
        return False

def test_dependencies():
    """测试依赖项"""
    print("🔍 测试依赖项...")
    
    dependencies = [
        "cv2",
        "numpy", 
        "torch",
        "ultralytics",
        "mss",
        "pyautogui",
        "win32api",
        "requests"
    ]
    
    missing = []
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep} - 缺失")
            missing.append(dep)
    
    if missing:
        print(f"\n⚠️ 缺失的依赖: {', '.join(missing)}")
        print("请安装缺失的依赖后重新编译")
        return False
    else:
        print("\n✅ 所有依赖项都已安装")
        return True

def create_simple_test():
    """创建简单测试版本"""
    print("🔧 创建简单测试版本...")
    
    simple_code = '''
import sys
import time

def main():
    print("HohoAI Test Version")
    print("Python version:", sys.version)
    print("Current directory:", sys.path[0])
    
    try:
        import cv2
        print("✅ OpenCV available")
    except ImportError:
        print("❌ OpenCV not available")
    
    try:
        import torch
        print("✅ PyTorch available")
    except ImportError:
        print("❌ PyTorch not available")
    
    try:
        from ultralytics import YOLO
        print("✅ YOLO available")
    except ImportError:
        print("❌ YOLO not available")
    
    print("Test completed. Press Enter to exit...")
    input()

if __name__ == "__main__":
    main()
'''
    
    with open("test_version.py", "w") as f:
        f.write(simple_code)
    
    # 编译测试版本
    compile_options = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--console",
        "--name", "test_hohoai",
        "test_version.py"
    ]
    
    try:
        subprocess.run(compile_options, check=True)
        print("✅ 测试版本编译成功: dist/test_hohoai.exe")
        return True
    except Exception as e:
        print(f"❌ 测试版本编译失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ HohoAI 调试编译器")
    
    while True:
        print("\n" + "="*50)
        print("选择操作:")
        print("1. 测试依赖项")
        print("2. 编译调试版本（带控制台）")
        print("3. 创建简单测试版本")
        print("4. 检查现有exe文件")
        print("0. 退出")
        
        choice = input("\n请选择 (0-4): ").strip()
        
        if choice == "0":
            break
        elif choice == "1":
            test_dependencies()
        elif choice == "2":
            compile_debug_version()
        elif choice == "3":
            create_simple_test()
        elif choice == "4":
            print("📁 检查dist目录:")
            if os.path.exists("dist"):
                for file in os.listdir("dist"):
                    if file.endswith(".exe"):
                        size = os.path.getsize(f"dist/{file}") / (1024*1024)
                        print(f"  {file}: {size:.1f} MB")
            else:
                print("  dist目录不存在")
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
