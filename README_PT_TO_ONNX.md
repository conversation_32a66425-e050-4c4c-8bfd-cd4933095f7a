# PyTorch 转 ONNX 转换器

这个工具包含两个脚本，用于将 PyTorch 模型 (.pt/.pth) 转换为 ONNX 格式。

## 文件说明

- `simple_pt_to_onnx.py` - 简单易用的转换脚本
- `pt_to_onnx_converter.py` - 功能完整的转换脚本，支持更多参数
- `README_PT_TO_ONNX.md` - 使用说明

## 环境要求

安装必要的 Python 包：

```bash
pip install torch torchvision onnx onnxruntime
```

或者如果需要 GPU 支持：

```bash
pip install torch torchvision onnx onnxruntime-gpu
```

## 快速使用 (推荐)

使用简单版本脚本：

```bash
# 基本用法
python simple_pt_to_onnx.py model.pt

# 指定输出文件名
python simple_pt_to_onnx.py model.pt output.onnx
```

## 高级使用

使用完整版本脚本，支持更多自定义选项：

```bash
# 基本转换
python pt_to_onnx_converter.py --input model.pt

# 自定义输入尺寸
python pt_to_onnx_converter.py --input model.pt --input_shape 1 3 224 224

# 使用 GPU 转换
python pt_to_onnx_converter.py --input model.pt --device cuda

# 转换后验证模型
python pt_to_onnx_converter.py --input model.pt --verify

# 完整参数示例
python pt_to_onnx_converter.py \
    --input model.pt \
    --output model.onnx \
    --input_shape 1 3 640 640 \
    --device cuda \
    --opset 11 \
    --verify
```

## 参数说明

### 完整版本参数

- `--input, -i`: 输入 PyTorch 模型路径 (必需)
- `--output, -o`: 输出 ONNX 模型路径 (可选，默认为输入文件名.onnx)
- `--input_shape, -s`: 输入张量形状 [batch_size, channels, height, width] (默认: 1 3 640 640)
- `--device, -d`: 设备类型 cpu/cuda (默认: cpu)
- `--opset`: ONNX opset 版本 (默认: 11)
- `--verify`: 转换后验证 ONNX 模型

## 常见输入尺寸

根据不同模型类型，常用的输入尺寸：

- **YOLOv8/YOLOv5**: `(1, 3, 640, 640)`
- **图像分类模型**: `(1, 3, 224, 224)`
- **ResNet**: `(1, 3, 224, 224)`
- **EfficientNet**: `(1, 3, 224, 224)` 或 `(1, 3, 380, 380)`

## 修改输入尺寸

如果需要修改默认输入尺寸，编辑 `simple_pt_to_onnx.py` 文件中的这一行：

```python
input_size = (1, 3, 640, 640)  # 修改为你需要的尺寸
```

格式: `(batch_size, channels, height, width)`

## 常见问题

### 1. 模型加载失败
- 确保 .pt 文件是完整的模型，不是只有 state_dict
- 检查 PyTorch 版本兼容性

### 2. 内存不足
- 使用较小的输入尺寸
- 使用 CPU 而不是 GPU

### 3. ONNX 版本问题
- 尝试不同的 opset 版本 (9, 10, 11, 12)
- 更新 onnx 包版本

### 4. 动态输入
脚本默认支持动态 batch_size，如果需要完全固定尺寸，可以移除 `dynamic_axes` 参数。

## 验证转换结果

转换完成后，可以使用以下代码验证 ONNX 模型：

```python
import onnx
import onnxruntime as ort
import numpy as np

# 加载并检查模型
model = onnx.load("model.onnx")
onnx.checker.check_model(model)

# 测试推理
session = ort.InferenceSession("model.onnx")
input_name = session.get_inputs()[0].name
output = session.run(None, {input_name: np.random.randn(1, 3, 640, 640).astype(np.float32)})
print("推理成功，输出形状:", output[0].shape)
```

## 注意事项

1. 确保原始 PyTorch 模型可以正常加载和推理
2. 转换过程中模型会自动设置为评估模式 (eval())
3. 如果模型包含自定义操作，可能需要额外处理
4. GPU 转换通常更快，但需要足够的显存

## 支持的模型类型

- YOLOv8/YOLOv5 检测模型
- 图像分类模型 (ResNet, EfficientNet, etc.)
- 语义分割模型
- 大部分标准 PyTorch 模型

如有问题，请检查错误信息并根据提示进行调整。
