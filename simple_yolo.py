import torch
import torch.onnx
import sys
import os

print("开始转换...")

if len(sys.argv) < 2:
    print("用法: python simple_yolo.py 示例模型/best.pt")
    exit()

pt_path = sys.argv[1]
print(f"模型路径: {pt_path}")

if not os.path.exists(pt_path):
    print(f"错误: 找不到文件 {pt_path}")
    exit()

print("加载模型...")
device = 'cuda' if torch.cuda.is_available() else 'cpu'
print(f"设备: {device}")

try:
    model = torch.load(pt_path, map_location=device, weights_only=False)
    print("模型加载成功")
    
    if isinstance(model, dict) and 'model' in model:
        model = model['model']
        print("提取模型对象")
    
    model = model.float()
    model.eval()
    print("模型转换为 float32")
    
    dummy_input = torch.randn(1, 3, 640, 640, dtype=torch.float32).to(device)
    print("创建输入张量")
    
    onnx_path = pt_path.replace('.pt', '.onnx')
    print(f"输出路径: {onnx_path}")
    
    print("开始 ONNX 转换...")
    torch.onnx.export(
        model, 
        dummy_input, 
        onnx_path,
        export_params=True, 
        opset_version=11,
        input_names=['images'],
        output_names=['output']
    )
    
    print("转换完成!")
    print(f"文件保存在: {onnx_path}")
    
    # 检查文件大小
    pt_size = os.path.getsize(pt_path) / (1024*1024)
    onnx_size = os.path.getsize(onnx_path) / (1024*1024)
    print(f"原始: {pt_size:.1f}MB -> ONNX: {onnx_size:.1f}MB")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
