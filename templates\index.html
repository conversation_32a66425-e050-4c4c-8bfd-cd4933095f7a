<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8">
    <title>卡密管理后台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>卡密管理系统</h2>
            <div>
                <a href="{{ url_for('logout') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-box-arrow-right"></i> 退出登录
                </a>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>添加新卡密</span>
            </div>
            <div class="card-body">
                <form action="{{ url_for('add_card') }}" method="POST">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <input type="text" class="form-control" name="key" placeholder="输入卡密" required>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" name="minutes">
                                <option value="60">1小时</option>
                                <option value="1440">1天</option>
                                <option value="10080">1周</option>
                                <option value="43200">1个月</option>
                                <option value="129600">3个月</option>
                                <option value="525600">1年</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">添加</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                卡密列表
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>卡密</th>
                                <th>状态</th>
                                <th>剩余时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for card in cards %}
                            <tr>
                                <td>
                                    <span class="card-key">{{ card.key }}</span>
                                    <i class="bi bi-clipboard copy-btn ms-2" onclick="copyToClipboard('{{ card.key }}')" 
                                       title="复制卡密"></i>
                                </td>
                                <td>
                                    {% if card.expired %}
                                        <span class="badge bg-danger">已过期</span>
                                    {% else %}
                                        <span class="badge bg-success">有效</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if card.expired %}
                                        <span class="expired-time">已过期</span>
                                    {% else %}
                                        <span class="time-left">{{ card.left_minutes }} 分钟</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteCard('{{ card.key }}')">
                                        删除
                                    </button>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center text-muted py-3">暂无卡密数据</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // 显示复制成功提示
                alert('已复制到剪贴板: ' + text);
            }, function(err) {
                console.error('复制失败: ', err);
                // 如果剪贴板API不可用，使用老式方法
                const textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();
                try {
                    const successful = document.execCommand('copy');
                    const msg = successful ? '成功' : '失败';
                    alert('复制' + msg + ': ' + text);
                } catch (err) {
                    console.error('复制失败: ', err);
                    alert('复制失败，请手动选择并复制');
                }
                document.body.removeChild(textarea);
            });
        }

        function deleteCard(key) {
            if (confirm('确定要删除此卡密吗？此操作不可恢复！')) {
                fetch('/delete_card', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ key: key })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除过程中出错');
                });
            }
        }

        // 自动刷新页面，每60秒一次
        setTimeout(function() {
            window.location.reload();
        }, 60000);
    </script>
</body>
</html>
