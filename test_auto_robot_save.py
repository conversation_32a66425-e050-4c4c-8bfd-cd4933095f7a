#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动机器人保存功能
"""

import sys
import os
import json
from pathlib import Path

# 添加Module路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'Module'))

try:
    from config import Config
except ImportError:
    print("无法导入Config模块，请确保Module/config.py存在")
    sys.exit(1)

def test_config_operations():
    """测试配置操作"""
    print("自动机器人配置保存测试")
    print("=" * 40)
    
    # 测试读取默认配置
    print("1. 测试读取默认配置...")
    auto_robot_enabled = Config.get("auto_robot_enabled", False)
    auto_robot_vk = Config.get("auto_robot_hotkey_vk", 71)
    auto_robot_hex = Config.get("auto_robot_hotkey_hex", "47")
    
    print(f"   自动机器人启用: {auto_robot_enabled}")
    print(f"   热键VK码: {auto_robot_vk}")
    print(f"   热键十六进制: {auto_robot_hex}")
    
    # 测试更新配置
    print("\n2. 测试更新配置...")
    Config.update("auto_robot_enabled", True)
    Config.update("auto_robot_hotkey_vk", 72)  # H键
    Config.update("auto_robot_hotkey_hex", "48")  # H键十六进制
    
    print("   已更新配置:")
    print("   - 启用自动机器人: True")
    print("   - 热键改为H键 (VK: 72, HEX: 48)")
    
    # 测试读取更新后的配置
    print("\n3. 测试读取更新后的配置...")
    auto_robot_enabled = Config.get("auto_robot_enabled")
    auto_robot_vk = Config.get("auto_robot_hotkey_vk")
    auto_robot_hex = Config.get("auto_robot_hotkey_hex")
    
    print(f"   自动机器人启用: {auto_robot_enabled}")
    print(f"   热键VK码: {auto_robot_vk}")
    print(f"   热键十六进制: {auto_robot_hex}")
    
    # 测试配置文件是否存在
    print("\n4. 检查配置文件...")
    config_file = Path("Data/settings.json")
    if config_file.exists():
        print(f"   ✅ 配置文件存在: {config_file}")
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"   配置文件内容预览:")
            for key in ["auto_robot_enabled", "auto_robot_hotkey_vk", "auto_robot_hotkey_hex"]:
                if key in data:
                    print(f"     {key}: {data[key]}")
        except Exception as e:
            print(f"   ❌ 读取配置文件失败: {e}")
    else:
        print(f"   ❌ 配置文件不存在: {config_file}")
    
    # 恢复默认配置
    print("\n5. 恢复默认配置...")
    Config.update("auto_robot_enabled", False)
    Config.update("auto_robot_hotkey_vk", 71)  # G键
    Config.update("auto_robot_hotkey_hex", "47")  # G键十六进制
    print("   已恢复默认配置")
    
    print("\n✅ 配置保存功能测试完成！")

def test_key_mapping():
    """测试按键映射"""
    print("\n按键映射测试")
    print("=" * 20)
    
    key_mappings = {
        "G": {"vk": 71, "hex": "47"},
        "H": {"vk": 72, "hex": "48"},
        "F": {"vk": 70, "hex": "46"},
        "X": {"vk": 88, "hex": "58"},
        "F1": {"vk": 112, "hex": "70"},
        "F2": {"vk": 113, "hex": "71"},
    }
    
    for key_name, info in key_mappings.items():
        vk = info["vk"]
        hex_code = info["hex"]
        calculated_hex = f"{vk:02X}"
        
        status = "✅" if hex_code.upper() == calculated_hex else "❌"
        print(f"{status} {key_name}键: VK={vk}, 预期HEX={hex_code}, 计算HEX={calculated_hex}")

def main():
    """主函数"""
    try:
        test_config_operations()
        test_key_mapping()
        
        print("\n" + "=" * 50)
        print("测试总结:")
        print("1. 配置读写功能正常")
        print("2. 自动机器人设置可以保存和加载")
        print("3. 热键映射计算正确")
        print("4. 主程序现在支持:")
        print("   - 启动时自动加载自动机器人设置")
        print("   - 更改热键时自动保存")
        print("   - 点击保存按钮时保存所有设置")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
