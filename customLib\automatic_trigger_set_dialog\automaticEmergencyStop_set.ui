<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>406</width>
    <height>219</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>0</y>
     <width>101</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="text">
    <string>自动急停配置</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignCenter</set>
   </property>
  </widget>
  <widget class="QRadioButton" name="radioButton_3">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>40</y>
     <width>131</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="text">
    <string>按下自瞄生效</string>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <attribute name="buttonGroup">
    <string notr="true">buttonGroup</string>
   </attribute>
  </widget>
  <widget class="QLabel" name="triggerPositionLabel">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>80</y>
     <width>71</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgba(0, 0, 0, 0);</string>
   </property>
   <property name="text">
    <string>适用游戏：</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
   </property>
  </widget>
  <widget class="QComboBox" name="triggerPositioncomboBox_2">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>80</y>
     <width>131</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Microsoft YaHei</family>
     <pointsize>10</pointsize>
    </font>
   </property>
   <item>
    <property name="text">
     <string>VALORANT</string>
    </property>
   </item>
   <item>
    <property name="text">
     <string>自定义（未启用）</string>
    </property>
   </item>
  </widget>
 </widget>
 <resources/>
 <connections/>
 <buttongroups>
  <buttongroup name="buttonGroup"/>
 </buttongroups>
</ui>
