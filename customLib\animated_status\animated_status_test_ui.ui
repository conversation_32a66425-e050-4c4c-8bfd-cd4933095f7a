<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>640</width>
    <height>480</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QWidget" name="statusDisplayWidget" native="true">
    <property name="geometry">
     <rect>
      <x>240</x>
      <y>70</y>
      <width>171</width>
      <height>41</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(44, 44, 44);</string>
    </property>
    <widget class="QLabel" name="statusDisplayLabel">
     <property name="geometry">
      <rect>
       <x>40</x>
       <y>5</y>
       <width>91</width>
       <height>31</height>
      </rect>
     </property>
     <property name="text">
      <string>测试</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
   </widget>
   <widget class="QPushButton" name="showButton">
    <property name="geometry">
     <rect>
      <x>70</x>
      <y>410</y>
      <width>141</width>
      <height>51</height>
     </rect>
    </property>
    <property name="text">
     <string>显示</string>
    </property>
   </widget>
   <widget class="QPushButton" name="hideButton">
    <property name="geometry">
     <rect>
      <x>270</x>
      <y>410</y>
      <width>141</width>
      <height>51</height>
     </rect>
    </property>
    <property name="text">
     <string>隐藏</string>
    </property>
   </widget>
   <widget class="QPushButton" name="quickCall">
    <property name="geometry">
     <rect>
      <x>460</x>
      <y>410</y>
      <width>141</width>
      <height>51</height>
     </rect>
    </property>
    <property name="text">
     <string>快捷调用</string>
    </property>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
