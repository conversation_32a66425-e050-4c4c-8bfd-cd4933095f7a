#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv8 专用转换器 (修正版)
解决数据类型不匹配和新版 PyTorch 兼容性问题
使用方法: python yolo_converter.py model.pt
"""

import torch
import torch.onnx
import sys
import os

def convert_yolo(pt_path):
    """转换 YOLOv8 模型到 ONNX 格式"""
    
    print("🚀 YOLOv8 转换器 (修正版)")
    print("=" * 40)
    
    # 检查文件是否存在
    if not os.path.exists(pt_path):
        print(f"❌ 错误: 找不到文件 {pt_path}")
        return False
    
    try:
        # 加载模型
        print(f"📁 加载模型: {pt_path}")
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"🖥️  设备: {device}")
        
        # 使用兼容模式加载模型
        model = torch.load(pt_path, map_location=device, weights_only=False)
        
        if isinstance(model, dict) and 'model' in model:
            model = model['model']
        
        # 关键修复1：转换模型到 float32，解决数据类型不匹配
        print("🔧 转换模型到 float32...")
        model = model.float()
        model.eval()
        
        # 关键修复2：创建正确类型的输入张量
        print("🔧 创建输入张量...")
        dummy_input = torch.randn(1, 3, 640, 640, dtype=torch.float32).to(device)
        
        # 显示类型信息
        model_dtype = next(model.parameters()).dtype
        input_dtype = dummy_input.dtype
        print(f"✅ 模型权重类型: {model_dtype}")
        print(f"✅ 输入张量类型: {input_dtype}")
        
        # 设置输出路径
        onnx_path = pt_path.replace('.pt', '.onnx').replace('.pth', '.onnx')
        
        print("🔄 开始转换到 ONNX...")
        print(f"📐 输入尺寸: {dummy_input.shape}")
        
        # 转换为 ONNX
        torch.onnx.export(
            model, 
            dummy_input, 
            onnx_path,
            export_params=True, 
            opset_version=11,
            do_constant_folding=True,
            input_names=['images'],  # YOLOv8 标准输入名
            output_names=['output'],
            dynamic_axes={
                'images': {0: 'batch_size'},    # 动态批次大小
                'output': {0: 'batch_size'}
            },
            verbose=False  # 减少输出信息
        )
        
        print(f"✅ 转换成功!")
        print(f"📄 输出文件: {onnx_path}")
        
        # 显示文件大小对比
        pt_size = os.path.getsize(pt_path) / (1024*1024)
        onnx_size = os.path.getsize(onnx_path) / (1024*1024)
        print(f"📊 文件大小对比:")
        print(f"   原始模型: {pt_size:.1f} MB")
        print(f"   ONNX模型: {onnx_size:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        print("\n💡 可能的解决方案:")
        print("1. 确保模型文件完整且未损坏")
        print("2. 检查是否为有效的 YOLOv8 模型")
        print("3. 尝试更新 PyTorch: pip install torch --upgrade")
        print("4. 确保安装了 ultralytics: pip install ultralytics")
        return False

# 主程序
if len(sys.argv) < 2:
    print("🎯 YOLOv8 专用 ONNX 转换器")
    print("解决数据类型不匹配和兼容性问题")
    print("=" * 50)
    print("📖 使用方法:")
    print("  python yolo_converter.py model.pt")
    print("  python yolo_converter.py 示例模型/best.pt")
    print("")
    print("💡 功能特点:")
    print("  ✅ 自动修复数据类型不匹配")
    print("  ✅ 兼容新版 PyTorch")
    print("  ✅ 专门优化 YOLOv8 模型")
else:
    pt_path = sys.argv[1]
    success = convert_yolo(pt_path)
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 转换完成!")
        print("现在可以在 ONNX Runtime 中使用您的模型了")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("💥 转换失败!")
        print("请检查错误信息并尝试解决")
        print("=" * 50)
