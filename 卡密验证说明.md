# HohoAI 卡密验证说明

## 🔐 卡密验证时机

现在所有组件都需要卡密验证：

### 1. 无头服务启动时
```bash
python headless_service.py
```
会提示：
```
Graphics Driver Service: Initializing headless mode...
Graphics Driver Service: Authentication required
Enter license key (attempt 1/3): 
```

### 2. 参数调整工具启动时
```bash
python adjust_params.py
```
会提示：
```
🎮 HohoAI 参数调整工具
Graphics Driver Service Configuration Tool
请输入卡密 (第1/3次): 
```

### 3. 主程序启动时
```bash
python hohoai.py
```
会提示卡密验证界面

## 🚀 推荐使用方式

### 方法1：使用启动器（推荐）
```bash
python start_service.py
```

选择启动模式：
- **1. 安全模式** - 需要卡密验证，完全隐蔽
- **2. 调试模式** - 跳过卡密验证，仅用于测试
- **3. 参数调整** - 启动参数调整工具

### 方法2：直接启动
```bash
# 安全模式（需要卡密）
python headless_service.py

# 调试模式（跳过卡密）
python start_service.py --skip-auth

# 参数调整（需要卡密）
python adjust_params.py
```

## 🔧 调试模式

如果您需要测试功能而不想每次输入卡密：

```bash
# 方法1：使用启动器
python start_service.py --skip-auth

# 方法2：设置环境变量
set SKIP_LICENSE_CHECK=true
python headless_service.py
```

⚠️ **注意：调试模式仅用于开发测试，正式使用请使用安全模式**

## 📋 完整的使用流程

### 正常使用流程：
1. **启动服务**：`python start_service.py` → 选择"1"
2. **输入卡密**：验证成功后服务开始运行
3. **调整参数**：`python adjust_params.py` → 输入卡密 → 编辑配置文件
4. **保存配置**：编辑完成后保存，服务自动应用新设置

### 快速测试流程：
1. **调试启动**：`python start_service.py --skip-auth`
2. **跳过验证**：直接启动服务
3. **调整参数**：`python adjust_params.py` → 输入卡密 → 编辑配置

## 🛡️ 安全特性

- ✅ **多重验证**：服务启动和参数调整都需要验证
- ✅ **网络验证**：连接远程服务器验证卡密
- ✅ **重试机制**：每次有3次输入机会
- ✅ **调试模式**：开发时可跳过验证
- ✅ **进程伪装**：验证过程也伪装成图形驱动服务

## 🎯 常见问题

### Q: 为什么需要两次输入卡密？
A: 
- 第一次：启动无头服务时验证
- 第二次：使用参数调整工具时验证
- 这样确保每个组件都经过授权

### Q: 可以保存卡密吗？
A: 出于安全考虑，不保存卡密，每次启动都需要验证

### Q: 调试模式安全吗？
A: 调试模式跳过验证，仅用于开发测试，正式使用请用安全模式

### Q: 网络验证失败怎么办？
A: 检查网络连接，确认验证服务器可访问

## 💡 使用建议

1. **正式使用**：始终使用安全模式
2. **开发测试**：可以使用调试模式提高效率
3. **参数调整**：建议先启动服务，再调整参数
4. **网络环境**：确保能访问验证服务器

## 🎮 一键启动脚本

您可以创建批处理文件快速启动：

**start_secure.bat**
```batch
@echo off
echo Starting HohoAI in secure mode...
python start_service.py --mode secure
pause
```

**start_debug.bat**
```batch
@echo off
echo Starting HohoAI in debug mode...
python start_service.py --skip-auth
pause
```

现在您有了完整的卡密验证体系！🔐
