[2025-08-04 14:16:51] Graphics Driver Service started
[2025-08-04 14:16:51] [INFO] Graphics Service: Performance optimization completed
[2025-08-04 14:16:51] [INFO] Graphics Service: Initializing YOLO model: yolov8n.pt
[2025-08-04 14:16:51] [WARNING] Graphics Service: Model file not found: yolov8n.pt, using default yolov8n.pt
[2025-08-04 14:16:51] [INFO] Graphics Service: Using standard YOLO loading
[2025-08-04 14:17:02] [INFO] Graphics Service: YOLO model initialized and warmed up successfully
[2025-08-04 14:17:02] [INFO] Graphics Service: YOLO model ready for detection
[2025-08-04 14:17:02] [INFO] Graphics Service: Starting video capture...
[2025-08-04 14:17:02] [INFO] Graphics Service: Starting video capture loop
[2025-08-04 14:17:02] [INFO] Graphics Service: Video capture started successfully
[2025-08-04 14:17:02] [INFO] Graphics Service: YOLO detection enabled
[2025-08-04 14:17:02] [INFO] Graphics Service: Auto-enabled YOLO detection based on configuration
[2025-08-04 14:17:02] [INFO] Graphics Service: Background service started successfully
[2025-08-04 14:17:02] [INFO] Graphics Service: Service is running in stealth mode (no GUI)
[2025-08-04 14:17:02] [INFO] Graphics Service: Configuration monitoring enabled - edit hohoai_config.json to change settings
[2025-08-04 14:17:02] [INFO] Graphics Service: Video capture is active
[2025-08-04 14:17:02] [INFO] Graphics Service: YOLO detection is active
[2025-08-04 14:17:10] [INFO] Graphics Service: Video capture: 100 frames processed
[2025-08-04 14:17:19] [INFO] Graphics Service: Video capture: 200 frames processed
