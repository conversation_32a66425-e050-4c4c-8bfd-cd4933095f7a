[2025-08-04 14:38:47] Graphics Driver Service started
[2025-08-04 14:38:47] [INFO] Graphics Service: KMBox module found, attempting to initialize...
[2025-08-04 14:38:47] [INFO] Graphics Service: Attempting to connect to KMBox at 192.168.2.188:18243
[2025-08-04 14:38:47] [INFO] Graphics Service: Initializing YOLO model: yolov8n.pt
[2025-08-04 14:38:47] [INFO] Graphics Service: Using standard YOLO loading
[2025-08-04 14:38:49] [INFO] Graphics Service: YOLO model initialized and warmed up successfully
[2025-08-04 14:38:49] [INFO] Graphics Service: YOLO model ready for detection
[2025-08-04 14:38:49] [INFO] Graphics Service: Starting video capture...
[2025-08-04 14:38:49] [INFO] Graphics Service: Starting video capture loop
[2025-08-04 14:38:49] [INFO] Graphics Service: Video capture started successfully
[2025-08-04 14:38:49] [INFO] Graphics Service: YOLO detection enabled
[2025-08-04 14:38:49] [INFO] Graphics Service: Auto-enabled YOLO detection based on configuration
[2025-08-04 14:38:49] [INFO] Graphics Service: Background service started successfully
[2025-08-04 14:38:49] [INFO] Graphics Service: Service is running in stealth mode (no GUI)
[2025-08-04 14:38:49] [INFO] Graphics Service: Configuration monitoring enabled - edit hohoai_config.json to change settings
[2025-08-04 14:38:49] [INFO] Graphics Service: Video capture is active
[2025-08-04 14:38:49] [INFO] Graphics Service: YOLO detection is active
[2025-08-04 14:39:12] [INFO] Graphics Service: Configuration file hohoai_config.json changed, reloading...
[2025-08-04 14:39:12] [INFO] Graphics Service: Loaded configuration from hohoai_config.json
[2025-08-04 14:39:12] [INFO] Graphics Service: Configuration changes applied successfully
