#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的 PyTorch 转 ONNX 转换器
使用方法: python simple_pt_to_onnx.py model.pt
"""

import torch
import torch.onnx
import os
import sys

def convert_pt_to_onnx(pt_path, onnx_path=None, input_size=(1, 3, 640, 640)):
    """
    将 PyTorch 模型转换为 ONNX
    
    Args:
        pt_path: .pt 模型文件路径
        onnx_path: 输出 .onnx 文件路径 (可选)
        input_size: 输入尺寸 (batch_size, channels, height, width)
    """
    
    # 检查文件是否存在
    if not os.path.exists(pt_path):
        print(f"错误: 找不到文件 {pt_path}")
        return False
    
    # 设置输出路径
    if onnx_path is None:
        onnx_path = pt_path.replace('.pt', '.onnx').replace('.pth', '.onnx')
    
    try:
        print(f"正在加载模型: {pt_path}")
        
        # 检查是否有 GPU
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"使用设备: {device}")
        
        # 加载模型
        model = torch.load(pt_path, map_location=device)
        
        # 处理不同的模型格式
        if isinstance(model, dict):
            if 'model' in model:
                model = model['model']
            elif 'state_dict' in model:
                print("错误: 检测到 state_dict，需要完整的模型文件")
                return False
        
        # 设置为评估模式
        model.eval()
        
        # 创建虚拟输入
        dummy_input = torch.randn(input_size).to(device)
        
        print(f"开始转换...")
        print(f"输入尺寸: {input_size}")
        
        # 转换为 ONNX
        torch.onnx.export(
            model,
            dummy_input,
            onnx_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            dynamic_axes={
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            }
        )
        
        print(f"✓ 转换成功!")
        print(f"输出文件: {onnx_path}")
        
        # 检查文件大小
        pt_size = os.path.getsize(pt_path) / (1024*1024)
        onnx_size = os.path.getsize(onnx_path) / (1024*1024)
        print(f"原始模型大小: {pt_size:.1f} MB")
        print(f"ONNX 模型大小: {onnx_size:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        return False

def main():
    print("=" * 50)
    print("简单 PyTorch 转 ONNX 转换器")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python simple_pt_to_onnx.py model.pt")
        print("  python simple_pt_to_onnx.py model.pt output.onnx")
        print("")
        print("示例:")
        print("  python simple_pt_to_onnx.py yolov8.pt")
        print("  python simple_pt_to_onnx.py model.pt model.onnx")
        return
    
    pt_path = sys.argv[1]
    onnx_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 可以修改这里的输入尺寸
    # 常见尺寸:
    # YOLOv8: (1, 3, 640, 640)
    # 分类模型: (1, 3, 224, 224)
    # 自定义: (batch_size, channels, height, width)
    input_size = (1, 3, 640, 640)
    
    success = convert_pt_to_onnx(pt_path, onnx_path, input_size)
    
    if success:
        print("=" * 50)
        print("转换完成! 🎉")
        print("=" * 50)
    else:
        print("=" * 50)
        print("转换失败! ❌")
        print("=" * 50)

if __name__ == "__main__":
    main()
