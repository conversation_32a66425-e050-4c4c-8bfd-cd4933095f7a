#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HohoAI 无头模式服务 - 图形驱动后台服务
完全无GUI的后台运行模式，避免反作弊检测
"""

import os
import sys
import time
import random
import threading
import signal
import ctypes
import json
import subprocess
import requests
import multiprocessing
import queue
import cv2
import numpy as np
import mss
import pyautogui
import win32api
import win32con
from pathlib import Path

# 尝试导入YOLO相关模块
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("⚠️ YOLO模块未安装，视频检测功能将被禁用")

try:
    from gpu_safe_yolo import create_gpu_safe_yolo
    USE_GPU_SAFE_YOLO = True
except ImportError:
    USE_GPU_SAFE_YOLO = False

# 进程伪装
try:
    ctypes.windll.kernel32.SetConsoleTitleW("Graphics Driver Background Service")
except:
    pass

class GraphicsDriverBackgroundService:
    """图形驱动后台服务类"""

    def __init__(self):
        self.running = False
        self.config = self._load_config()
        self.service_name = "Graphics Driver Background Service"
        self.version = "27.20.100.8681"

        # 视频和YOLO相关
        self.video_running = False
        self.yolo_enabled = False
        self.model = None
        self.video_thread = None
        self.yolo_thread = None

        # KMBox相关
        self.kmbox_enabled = False
        self.kmbox_device = None
        self.movement_method = "software"  # "software", "kmbox"

        # 检测统计
        self.detection_stats = {
            "total_frames": 0,
            "frames_with_targets": 0,
            "total_targets": 0,
            "targets_in_range": 0,
            "last_detection_time": None
        }

        # 防止日志刷屏
        self.last_log_time = 0
        self.last_target_info = None
        
    def _load_config(self):
        """加载服务配置"""
        default_config = {
            "service_mode": "background",
            "monitoring_interval": 60,
            "auto_optimization": True,
            "log_level": "INFO",
            "stealth_mode": True
        }

        # 尝试加载主配置文件
        config_files = ["hohoai_config.json", "graphics_service_config.json"]

        for config_file in config_files:
            try:
                if os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        user_config = json.load(f)
                        default_config.update(user_config)
                        self._log(f"Loaded configuration from {config_file}")
                        break
            except Exception as e:
                self._log(f"Failed to load {config_file}: {e}", "ERROR")

        return default_config

    def _watch_config_changes(self):
        """监控配置文件变化"""
        config_files = ["hohoai_config.json", "graphics_service_config.json"]
        last_modified = {}

        # 初始化文件修改时间
        for config_file in config_files:
            if os.path.exists(config_file):
                last_modified[config_file] = os.path.getmtime(config_file)

        while self.running:
            try:
                for config_file in config_files:
                    if os.path.exists(config_file):
                        current_modified = os.path.getmtime(config_file)
                        if config_file not in last_modified or current_modified > last_modified[config_file]:
                            self._log(f"Configuration file {config_file} changed, reloading...")
                            self.config = self._load_config()
                            last_modified[config_file] = current_modified
                            self._apply_config_changes()

                time.sleep(5)  # 每5秒检查一次

            except Exception as e:
                self._log(f"Config monitoring error: {e}", "ERROR")
                time.sleep(30)

    def _apply_config_changes(self):
        """应用配置变化"""
        try:
            # 更新监控间隔
            if "stealth_settings" in self.config:
                stealth = self.config["stealth_settings"]
                if "monitoring_interval" in stealth:
                    self.config["monitoring_interval"] = stealth["monitoring_interval"]

            self._log("Configuration changes applied successfully")

            # 这里可以添加更多配置应用逻辑
            # 比如发送信号给主程序更新参数

        except Exception as e:
            self._log(f"Failed to apply config changes: {e}", "ERROR")

    def _initialize_kmbox(self):
        """初始化KMBox设备"""
        try:
            # 尝试导入KMBox模块
            try:
                from Module.control import kmNet
                self._log("KMBox module found, attempting to initialize...")
            except ImportError:
                self._log("KMBox module not found, using software mouse control", "WARNING")
                return False

            # 获取KMBox配置
            kmbox_settings = self.config.get("kmbox_settings", {})
            enabled = kmbox_settings.get("enabled", False)

            if not enabled:
                self._log("KMBox disabled in configuration")
                return False

            # KMBox连接参数
            ip = kmbox_settings.get("ip", "*************")
            port = kmbox_settings.get("port", 8888)
            uuid = kmbox_settings.get("uuid", "6EC5D1A1")

            self._log(f"Attempting to connect to KMBox at {ip}:{port}")

            # 初始化KMBox连接
            result = kmNet.init(ip, str(port), uuid)  # 端口转换为字符串
            if result == 0:
                self._log("✅ KMBox connected successfully!")
                self.kmbox_enabled = True
                self.movement_method = "kmbox"

                # 测试KMBox连接
                self._test_kmbox_connection()
                return True
            else:
                self._log(f"❌ KMBox connection failed with code: {result}", "ERROR")
                return False

        except Exception as e:
            self._log(f"KMBox initialization error: {e}", "ERROR")
            return False

    def _test_kmbox_connection(self):
        """测试KMBox连接"""
        try:
            from Module.control import kmNet

            self._log("🔧 Testing KMBox connection...")

            # 尝试获取当前鼠标位置（不同版本的kmNet可能有不同的函数名）
            try:
                if hasattr(kmNet, 'getxy'):
                    current_x, current_y = kmNet.getxy()
                    self._log(f"📍 Current mouse position: ({current_x}, {current_y})")
                elif hasattr(kmNet, 'get_position'):
                    current_x, current_y = kmNet.get_position()
                    self._log(f"📍 Current mouse position: ({current_x}, {current_y})")
                else:
                    self._log("📍 Mouse position query not available, but movement should work")
            except Exception as e:
                self._log(f"📍 Mouse position query failed: {e}, but movement should work")

            # 执行小幅移动测试
            test_move_x, test_move_y = 5, 5
            kmNet.move(test_move_x, test_move_y)
            self._log(f"🖱️ KMBox test move: ({test_move_x}, {test_move_y})")

            # 等待一下再移动回去
            time.sleep(0.1)
            kmNet.move(-test_move_x, -test_move_y)
            self._log("✅ KMBox movement test completed successfully")

        except Exception as e:
            self._log(f"KMBox test failed: {e}", "ERROR")
            self.kmbox_enabled = False
            self.movement_method = "software"

    def _initialize_yolo(self):
        """初始化YOLO模型"""
        if not YOLO_AVAILABLE:
            self._log("YOLO not available, skipping model initialization", "WARNING")
            return False

        try:
            model_settings = self.config.get("model_settings", {})
            model_file = model_settings.get("model_file", "yolov8n.pt")
            use_gpu_safe = model_settings.get("use_gpu_safe_yolo", True)

            self._log(f"Initializing YOLO model: {model_file}")

            # 检查模型文件
            if not os.path.exists(model_file):
                self._log(f"Model file not found: {model_file}, using default yolov8n.pt", "WARNING")
                model_file = "yolov8n.pt"

            # 加载模型
            if USE_GPU_SAFE_YOLO and use_gpu_safe:
                self._log("Using GPU-safe YOLO loading")
                self.model = create_gpu_safe_yolo(model_file, prefer_gpu=True)
            else:
                self._log("Using standard YOLO loading")
                self.model = YOLO(model_file)

            # 预热模型
            temp_img = np.zeros((320, 320, 3), dtype=np.uint8)
            self.model.predict(temp_img, conf=0.5, verbose=False)

            self._log("YOLO model initialized and warmed up successfully")
            return True

        except Exception as e:
            self._log(f"Failed to initialize YOLO model: {e}", "ERROR")
            return False

    def _video_capture_loop(self):
        """视频捕获循环"""
        self._log("Starting video capture loop")

        try:
            with mss.mss() as sct:
                # 获取屏幕中心区域
                screen_width = 1920  # 可以从配置读取
                screen_height = 1080
                capture_width, capture_height = 200, 200

                left = (screen_width - capture_width) // 2
                top = (screen_height - capture_height) // 2

                capture_area = {
                    "top": top,
                    "left": left,
                    "width": capture_width,
                    "height": capture_height,
                }

                frame_count = 0

                while self.video_running and self.running:
                    try:
                        # 捕获屏幕
                        img = sct.grab(capture_area)
                        frame = np.frombuffer(img.rgb, dtype=np.uint8).reshape((img.height, img.width, 3))
                        frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2RGB)

                        # 如果启用YOLO，进行检测
                        if self.yolo_enabled and self.model is not None:
                            self._process_frame_with_yolo(frame)

                        frame_count += 1

                        # 每100帧记录一次状态和统计
                        if frame_count % 100 == 0:
                            self._log(f"📹 视频处理: {frame_count} 帧 | 捕获区域: {capture_width}x{capture_height} | 位置: ({left}, {top})")
                            self._log_detection_stats()

                        # 控制帧率 (约20 FPS)
                        time.sleep(0.05)

                    except Exception as e:
                        self._log(f"Video capture error: {e}", "ERROR")
                        time.sleep(1)

        except Exception as e:
            self._log(f"Video capture loop failed: {e}", "ERROR")

        self._log("Video capture loop stopped")

    def _process_frame_with_yolo(self, frame):
        """使用YOLO处理帧"""
        try:
            # 更新统计
            self.detection_stats["total_frames"] += 1

            aimbot_settings = self.config.get("aimbot_settings", {})
            confidence = aimbot_settings.get("yolo_confidence", 0.5)
            target_class = aimbot_settings.get("target_class", "ALL")

            # 确定检测类别
            if target_class == "ALL":
                classes = None
            else:
                try:
                    classes = [int(target_class)]
                except ValueError:
                    classes = None

            # 执行YOLO推理
            results = self.model.predict(
                frame,
                save=False,
                verbose=False,
                conf=confidence,
                classes=classes
            )

            # 获取检测结果
            if len(results) > 0 and len(results[0].boxes) > 0:
                boxes = results[0].boxes.xyxy
                num_detections = len(boxes)

                # 计算最近的目标
                if num_detections > 0:
                    # 更新统计
                    self.detection_stats["frames_with_targets"] += 1
                    self.detection_stats["total_targets"] += num_detections
                    self.detection_stats["last_detection_time"] = time.time()

                    frame_center = (frame.shape[1] / 2, frame.shape[0] / 2)
                    min_distance = float('inf')
                    closest_box = None

                    for box in boxes:
                        x1, y1, x2, y2 = box.cpu().numpy()
                        box_center = ((x1 + x2) / 2, (y1 + y2) / 2)
                        distance = np.sqrt((box_center[0] - frame_center[0])**2 + (box_center[1] - frame_center[1])**2)

                        if distance < min_distance:
                            min_distance = distance
                            closest_box = (x1, y1, x2, y2)

                    # 防止重复日志（只有目标信息变化时才记录）
                    current_target_info = f"{num_detections}_{min_distance:.1f}"
                    current_time = time.time()

                    if (self.last_target_info != current_target_info or
                        current_time - self.last_log_time > 2.0):  # 至少2秒间隔

                        target_type = "人物" if target_class == "0" else "车辆" if target_class == "1" else "目标"
                        self._log(f"🎯 检测到 {num_detections} 个{target_type}，最近距离 {min_distance:.1f} 像素")

                        self.last_target_info = current_target_info
                        self.last_log_time = current_time

                    # 执行瞄准逻辑
                    self._handle_target_detection(closest_box, min_distance)
            else:
                # 没有检测到目标时，偶尔记录
                if hasattr(self, '_no_target_count'):
                    self._no_target_count += 1
                else:
                    self._no_target_count = 1

                # 每500帧记录一次"无目标"状态
                if self._no_target_count % 500 == 0:
                    self._log("🔍 扫描中... 暂未发现目标")

        except Exception as e:
            self._log(f"YOLO processing error: {e}", "ERROR")

    def _handle_target_detection(self, box, distance):
        """处理目标检测结果"""
        try:
            aimbot_settings = self.config.get("aimbot_settings", {})
            aim_range = aimbot_settings.get("aim_range", 100)

            # 计算目标信息
            x1, y1, x2, y2 = box
            target_center_x = (x1 + x2) / 2
            target_center_y = (y1 + y2) / 2
            target_width = x2 - x1
            target_height = y2 - y1

            # 计算偏移
            frame_center_x = 100  # 200/2
            frame_center_y = 100  # 200/2

            offset_x = target_center_x - frame_center_x
            offset_y = target_center_y - frame_center_y

            if distance < aim_range:
                # 目标在瞄准范围内
                self._log(f"🎯 目标锁定！距离: {distance:.1f}px, 偏移: ({offset_x:.1f}, {offset_y:.1f}), 大小: {target_width:.0f}x{target_height:.0f}")

                # 根据距离判断瞄准精度
                if distance < aim_range * 0.3:
                    precision = "精确瞄准"
                elif distance < aim_range * 0.6:
                    precision = "中等精度"
                else:
                    precision = "粗略瞄准"

                self._log(f"🎮 瞄准状态: {precision} | 建议移动: X{offset_x:+.1f}, Y{offset_y:+.1f}")

                # 执行自动瞄准
                self._perform_auto_aim(offset_x, offset_y, distance, precision)

                # 更新统计
                self.detection_stats["targets_in_range"] += 1

            else:
                # 目标超出瞄准范围
                self._log(f"👁️ 发现目标但超出范围: 距离 {distance:.1f}px > {aim_range}px")

        except Exception as e:
            self._log(f"Target handling error: {e}", "ERROR")

    def _perform_auto_aim(self, offset_x, offset_y, distance, precision):
        """执行自动瞄准"""
        try:
            aimbot_settings = self.config.get("aimbot_settings", {})

            # 检查是否启用自动瞄准
            if not aimbot_settings.get("enabled", True):
                return

            # 获取瞄准速度设置
            aim_speed_x = aimbot_settings.get("aim_speed_x", 0.2)
            aim_speed_y = aimbot_settings.get("aim_speed_y", 0.0)

            # 获取偏移设置
            offset_center_x = aimbot_settings.get("offset_centerx", 0)
            offset_center_y = aimbot_settings.get("offset_centery", 0.3)

            # 计算实际移动距离（应用速度和偏移）
            move_x = offset_x * aim_speed_x + offset_center_x
            move_y = offset_y * aim_speed_y + offset_center_y

            # 根据精度调整移动幅度
            if precision == "精确瞄准":
                move_multiplier = 0.8  # 精确时移动更小
            elif precision == "中等精度":
                move_multiplier = 1.0  # 正常移动
            else:
                move_multiplier = 1.2  # 粗略时移动更大

            move_x *= move_multiplier
            move_y *= move_multiplier

            # 限制移动范围，避免过大的移动
            max_move = 50
            move_x = max(-max_move, min(max_move, move_x))
            move_y = max(-max_move, min(max_move, move_y))

            # 调试日志：显示计算的移动距离
            self._log(f"🔧 计算移动: 原始偏移({offset_x:.1f}, {offset_y:.1f}) → 最终移动({move_x:.1f}, {move_y:.1f})")

            # 执行鼠标移动（降低阈值，让小幅移动也能执行）
            if abs(move_x) > 0.5 or abs(move_y) > 0.5:  # 降低阈值从1到0.5
                if self.movement_method == "kmbox" and self.kmbox_enabled:
                    # 使用KMBox硬件移动
                    self._kmbox_move(move_x, move_y, precision)
                else:
                    # 使用软件移动
                    self._software_move(move_x, move_y, precision)
            else:
                self._log(f"⚠️ 移动距离太小，跳过: ({move_x:.1f}, {move_y:.1f})")

        except Exception as e:
            self._log(f"Auto aim error: {e}", "ERROR")

    def _kmbox_move(self, move_x, move_y, precision):
        """使用KMBox执行鼠标移动"""
        try:
            from Module.control import kmNet

            # KMBox移动（相对移动）
            kmNet.move(int(move_x), int(move_y))
            self._log(f"🎮 KMBox瞄准: 移动 ({move_x:.1f}, {move_y:.1f}) | {precision}")

        except Exception as e:
            self._log(f"KMBox move error: {e}, falling back to software", "ERROR")
            # KMBox失败时回退到软件移动
            self._software_move(move_x, move_y, precision)

    def _software_move(self, move_x, move_y, precision):
        """使用软件执行鼠标移动"""
        try:
            # 使用win32api进行相对移动（更精确）
            win32api.mouse_event(win32con.MOUSEEVENTF_MOVE, int(move_x), int(move_y), 0, 0)
            self._log(f"🖱️ 软件瞄准: 移动 ({move_x:.1f}, {move_y:.1f}) | {precision}")
        except Exception as e:
            try:
                # 如果win32api失败，尝试pyautogui
                current_x, current_y = pyautogui.position()
                new_x = current_x + move_x
                new_y = current_y + move_y
                pyautogui.moveTo(new_x, new_y, duration=0.01)
                self._log(f"🖱️ PyAutoGUI瞄准: 移动到 ({new_x:.0f}, {new_y:.0f}) | {precision}")
            except Exception as e2:
                self._log(f"All mouse movement methods failed: {e}, {e2}", "ERROR")

        except Exception as e:
            self._log(f"Auto aim error: {e}", "ERROR")

    def start_video_capture(self):
        """启动视频捕获"""
        if self.video_running:
            self._log("Video capture already running")
            return

        self._log("Starting video capture...")
        self.video_running = True

        # 启动视频捕获线程
        self.video_thread = threading.Thread(target=self._video_capture_loop, daemon=True)
        self.video_thread.start()

        self._log("Video capture started successfully")

    def stop_video_capture(self):
        """停止视频捕获"""
        if not self.video_running:
            return

        self._log("Stopping video capture...")
        self.video_running = False

        if self.video_thread:
            self.video_thread.join(timeout=5)

        self._log("Video capture stopped")

    def enable_yolo(self):
        """启用YOLO检测"""
        if not self.model:
            self._log("YOLO model not initialized, cannot enable detection", "ERROR")
            return False

        self.yolo_enabled = True
        self._log("YOLO detection enabled")
        return True

    def disable_yolo(self):
        """禁用YOLO检测"""
        self.yolo_enabled = False
        self._log("YOLO detection disabled")

    def _log_detection_stats(self):
        """记录检测统计信息"""
        try:
            stats = self.detection_stats
            total_frames = stats["total_frames"]

            if total_frames > 0:
                detection_rate = (stats["frames_with_targets"] / total_frames) * 100
                avg_targets = stats["total_targets"] / max(stats["frames_with_targets"], 1)

                # 计算距离上次检测的时间
                if stats["last_detection_time"]:
                    time_since_last = time.time() - stats["last_detection_time"]
                    if time_since_last < 60:
                        last_detection = f"{time_since_last:.1f}秒前"
                    else:
                        last_detection = f"{time_since_last/60:.1f}分钟前"
                else:
                    last_detection = "从未检测到"

                self._log(f"📊 检测统计: 检测率 {detection_rate:.1f}% | 平均目标数 {avg_targets:.1f} | 上次检测 {last_detection}")

        except Exception as e:
            self._log(f"Statistics logging error: {e}", "ERROR")
    
    def _create_service_files(self):
        """创建服务相关文件"""
        service_files = {
            "graphics_driver_service.log": f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Graphics Driver Service started\n",
            "gpu_monitoring.dat": f"GPU_STATUS=ACTIVE\nLAST_CHECK={int(time.time())}\n",
            "display_config.ini": "[Display]\nResolution=1920x1080\nRefreshRate=144\nColorDepth=32\n",
            "service_status.tmp": f"SERVICE_PID={os.getpid()}\nSTATUS=RUNNING\nSTART_TIME={time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        }
        
        for filename, content in service_files.items():
            try:
                with open(filename, 'w') as f:
                    f.write(content)
            except:
                pass
    
    def _log(self, message, level="INFO"):
        """服务日志记录"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] [{level}] Graphics Service: {message}"
        print(log_message)
        
        # 写入日志文件
        try:
            with open("graphics_driver_service.log", 'a') as f:
                f.write(log_message + "\n")
        except:
            pass
    
    def _system_monitoring(self):
        """系统监控线程"""
        while self.running:
            try:
                # 模拟GPU监控
                gpu_usage = random.randint(10, 80)
                memory_usage = random.randint(20, 60)
                temperature = random.randint(45, 75)
                
                # 更新监控数据
                monitoring_data = f"GPU_USAGE={gpu_usage}%\nMEMORY_USAGE={memory_usage}%\nTEMPERATURE={temperature}C\nLAST_UPDATE={int(time.time())}\n"
                
                try:
                    with open("gpu_monitoring.dat", 'w') as f:
                        f.write(monitoring_data)
                except:
                    pass
                
                # 随机日志输出
                if random.random() < 0.1:  # 10% 概率
                    self._log(f"System monitoring - GPU: {gpu_usage}%, Memory: {memory_usage}%, Temp: {temperature}°C")
                
                # 模拟性能优化
                if random.random() < 0.05:  # 5% 概率
                    self._log("Performance optimization completed")
                
                # 等待下次检查
                time.sleep(self.config.get("monitoring_interval", 60))
                
            except Exception as e:
                self._log(f"Monitoring error: {e}", "ERROR")
                time.sleep(30)
    
    def _driver_maintenance(self):
        """驱动维护线程"""
        while self.running:
            try:
                # 模拟驱动维护任务
                maintenance_tasks = [
                    "Memory defragmentation",
                    "Cache optimization", 
                    "Registry cleanup",
                    "Driver integrity check",
                    "Hardware compatibility verification"
                ]
                
                # 随机执行维护任务
                if random.random() < 0.02:  # 2% 概率
                    task = random.choice(maintenance_tasks)
                    self._log(f"Executing maintenance task: {task}")
                    time.sleep(random.uniform(5, 15))
                    self._log(f"Maintenance task completed: {task}")
                
                time.sleep(random.uniform(300, 600))  # 5-10分钟间隔
                
            except Exception as e:
                self._log(f"Maintenance error: {e}", "ERROR")
                time.sleep(60)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self._log("Received shutdown signal")
        self.stop()
    
    def start(self):
        """启动后台服务"""
        self._log(f"Starting {self.service_name} v{self.version}")
        
        # 创建服务文件
        self._create_service_files()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.running = True
        
        # 启动监控线程
        monitoring_thread = threading.Thread(target=self._system_monitoring, daemon=True)
        monitoring_thread.start()
        
        # 启动维护线程
        maintenance_thread = threading.Thread(target=self._driver_maintenance, daemon=True)
        maintenance_thread.start()

        # 启动配置监控线程
        config_thread = threading.Thread(target=self._watch_config_changes, daemon=True)
        config_thread.start()

        # 初始化KMBox设备
        self._initialize_kmbox()

        # 初始化YOLO模型
        if self._initialize_yolo():
            self._log("YOLO model ready for detection")

            # 自动启动视频捕获
            self.start_video_capture()

            # 检查是否自动启用YOLO
            aimbot_settings = self.config.get("aimbot_settings", {})
            if aimbot_settings.get("enabled", True):
                self.enable_yolo()
                self._log("Auto-enabled YOLO detection based on configuration")
            else:
                self._log("YOLO detection disabled in configuration")
        else:
            self._log("YOLO initialization failed, running without detection", "WARNING")

        self._log("Background service started successfully")
        self._log("Service is running in stealth mode (no GUI)")
        self._log("Configuration monitoring enabled - edit hohoai_config.json to change settings")

        if self.video_running:
            self._log("Video capture is active")
        if self.yolo_enabled:
            self._log("YOLO detection is active")
        
        try:
            # 保持服务运行
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """停止后台服务"""
        self._log("Shutting down background service...")
        self.running = False

        # 停止视频捕获
        self.stop_video_capture()

        # 禁用YOLO
        self.disable_yolo()

        # 清理服务文件
        cleanup_files = [
            "service_status.tmp",
            "gpu_monitoring.dat"
        ]

        for filename in cleanup_files:
            try:
                if os.path.exists(filename):
                    os.remove(filename)
            except:
                pass

        self._log("Background service stopped")
        sys.exit(0)

def verify_card_key():
    """卡密验证"""
    print("Graphics Driver Service: Authentication required")

    # 卡密验证API
    CARD_API_URL = "http://************:5000/api/check_card"
    API_TOKEN = "3f8c1a9e4b9d63a01e6d2c14c956aa7b"

    for attempt in range(3):
        try:
            card_key = input(f"Enter license key (attempt {attempt+1}/3): ").strip()

            if not card_key:
                print("❌ License key cannot be empty")
                continue

            print("Graphics Service: Validating license...")

            # 发送验证请求
            response = requests.post(
                CARD_API_URL,
                json={"card_key": card_key},
                headers={"X-API-Token": API_TOKEN},
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("valid"):
                    print("✅ Graphics Service: License validation successful")
                    return True
                else:
                    print(f"❌ Graphics Service: License validation failed - {data.get('msg', 'Unknown error')}")
            else:
                print(f"❌ Graphics Service: Server error - {response.status_code}")

        except requests.exceptions.RequestException as e:
            print(f"❌ Graphics Service: Network error - {e}")
        except Exception as e:
            print(f"❌ Graphics Service: Validation error - {e}")

    print("❌ Graphics Service: License validation failed, service cannot start")
    return False

def start_headless_service():
    """启动无头模式服务"""
    print("Graphics Driver Service: Initializing headless mode...")

    # 检查是否跳过卡密验证（调试模式）
    skip_auth = os.environ.get('SKIP_LICENSE_CHECK', '').lower() == 'true'

    if skip_auth:
        print("🔧 Graphics Service: Debug mode - skipping license validation")
    else:
        # 卡密验证
        if not verify_card_key():
            print("Graphics Service: Unauthorized access denied")
            sys.exit(1)
        print("Graphics Service: Authorization confirmed, starting service...")

    service = GraphicsDriverBackgroundService()
    service.start()

if __name__ == "__main__":
    # 直接运行无头服务
    start_headless_service()
