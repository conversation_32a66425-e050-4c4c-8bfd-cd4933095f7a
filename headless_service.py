#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HohoAI 无头模式服务 - 图形驱动后台服务
完全无GUI的后台运行模式，避免反作弊检测
"""

import os
import sys
import time
import random
import threading
import signal
import ctypes
import json
import subprocess
import requests
import multiprocessing
import queue
import cv2
import numpy as np
import mss
from pathlib import Path

# 尝试导入YOLO相关模块
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("⚠️ YOLO模块未安装，视频检测功能将被禁用")

try:
    from gpu_safe_yolo import create_gpu_safe_yolo
    USE_GPU_SAFE_YOLO = True
except ImportError:
    USE_GPU_SAFE_YOLO = False

# 进程伪装
try:
    ctypes.windll.kernel32.SetConsoleTitleW("Graphics Driver Background Service")
except:
    pass

class GraphicsDriverBackgroundService:
    """图形驱动后台服务类"""

    def __init__(self):
        self.running = False
        self.config = self._load_config()
        self.service_name = "Graphics Driver Background Service"
        self.version = "27.20.100.8681"

        # 视频和YOLO相关
        self.video_running = False
        self.yolo_enabled = False
        self.model = None
        self.video_thread = None
        self.yolo_thread = None
        
    def _load_config(self):
        """加载服务配置"""
        default_config = {
            "service_mode": "background",
            "monitoring_interval": 60,
            "auto_optimization": True,
            "log_level": "INFO",
            "stealth_mode": True
        }

        # 尝试加载主配置文件
        config_files = ["hohoai_config.json", "graphics_service_config.json"]

        for config_file in config_files:
            try:
                if os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        user_config = json.load(f)
                        default_config.update(user_config)
                        self._log(f"Loaded configuration from {config_file}")
                        break
            except Exception as e:
                self._log(f"Failed to load {config_file}: {e}", "ERROR")

        return default_config

    def _watch_config_changes(self):
        """监控配置文件变化"""
        config_files = ["hohoai_config.json", "graphics_service_config.json"]
        last_modified = {}

        # 初始化文件修改时间
        for config_file in config_files:
            if os.path.exists(config_file):
                last_modified[config_file] = os.path.getmtime(config_file)

        while self.running:
            try:
                for config_file in config_files:
                    if os.path.exists(config_file):
                        current_modified = os.path.getmtime(config_file)
                        if config_file not in last_modified or current_modified > last_modified[config_file]:
                            self._log(f"Configuration file {config_file} changed, reloading...")
                            self.config = self._load_config()
                            last_modified[config_file] = current_modified
                            self._apply_config_changes()

                time.sleep(5)  # 每5秒检查一次

            except Exception as e:
                self._log(f"Config monitoring error: {e}", "ERROR")
                time.sleep(30)

    def _apply_config_changes(self):
        """应用配置变化"""
        try:
            # 更新监控间隔
            if "stealth_settings" in self.config:
                stealth = self.config["stealth_settings"]
                if "monitoring_interval" in stealth:
                    self.config["monitoring_interval"] = stealth["monitoring_interval"]

            self._log("Configuration changes applied successfully")

            # 这里可以添加更多配置应用逻辑
            # 比如发送信号给主程序更新参数

        except Exception as e:
            self._log(f"Failed to apply config changes: {e}", "ERROR")

    def _initialize_yolo(self):
        """初始化YOLO模型"""
        if not YOLO_AVAILABLE:
            self._log("YOLO not available, skipping model initialization", "WARNING")
            return False

        try:
            model_settings = self.config.get("model_settings", {})
            model_file = model_settings.get("model_file", "yolov8n.pt")
            use_gpu_safe = model_settings.get("use_gpu_safe_yolo", True)

            self._log(f"Initializing YOLO model: {model_file}")

            # 检查模型文件
            if not os.path.exists(model_file):
                self._log(f"Model file not found: {model_file}, using default yolov8n.pt", "WARNING")
                model_file = "yolov8n.pt"

            # 加载模型
            if USE_GPU_SAFE_YOLO and use_gpu_safe:
                self._log("Using GPU-safe YOLO loading")
                self.model = create_gpu_safe_yolo(model_file, prefer_gpu=True)
            else:
                self._log("Using standard YOLO loading")
                self.model = YOLO(model_file)

            # 预热模型
            temp_img = np.zeros((320, 320, 3), dtype=np.uint8)
            self.model.predict(temp_img, conf=0.5, verbose=False)

            self._log("YOLO model initialized and warmed up successfully")
            return True

        except Exception as e:
            self._log(f"Failed to initialize YOLO model: {e}", "ERROR")
            return False

    def _video_capture_loop(self):
        """视频捕获循环"""
        self._log("Starting video capture loop")

        try:
            with mss.mss() as sct:
                # 获取屏幕中心区域
                screen_width = 1920  # 可以从配置读取
                screen_height = 1080
                capture_width, capture_height = 200, 200

                left = (screen_width - capture_width) // 2
                top = (screen_height - capture_height) // 2

                capture_area = {
                    "top": top,
                    "left": left,
                    "width": capture_width,
                    "height": capture_height,
                }

                frame_count = 0

                while self.video_running and self.running:
                    try:
                        # 捕获屏幕
                        img = sct.grab(capture_area)
                        frame = np.frombuffer(img.rgb, dtype=np.uint8).reshape((img.height, img.width, 3))
                        frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2RGB)

                        # 如果启用YOLO，进行检测
                        if self.yolo_enabled and self.model is not None:
                            self._process_frame_with_yolo(frame)

                        frame_count += 1

                        # 每100帧记录一次状态
                        if frame_count % 100 == 0:
                            self._log(f"Video capture: {frame_count} frames processed")

                        # 控制帧率 (约20 FPS)
                        time.sleep(0.05)

                    except Exception as e:
                        self._log(f"Video capture error: {e}", "ERROR")
                        time.sleep(1)

        except Exception as e:
            self._log(f"Video capture loop failed: {e}", "ERROR")

        self._log("Video capture loop stopped")

    def _process_frame_with_yolo(self, frame):
        """使用YOLO处理帧"""
        try:
            aimbot_settings = self.config.get("aimbot_settings", {})
            confidence = aimbot_settings.get("yolo_confidence", 0.5)
            target_class = aimbot_settings.get("target_class", "ALL")

            # 确定检测类别
            if target_class == "ALL":
                classes = None
            else:
                try:
                    classes = [int(target_class)]
                except ValueError:
                    classes = None

            # 执行YOLO推理
            results = self.model.predict(
                frame,
                save=False,
                verbose=False,
                conf=confidence,
                classes=classes
            )

            # 获取检测结果
            if len(results) > 0 and len(results[0].boxes) > 0:
                boxes = results[0].boxes.xyxy
                num_detections = len(boxes)

                # 计算最近的目标
                if num_detections > 0:
                    frame_center = (frame.shape[1] / 2, frame.shape[0] / 2)
                    min_distance = float('inf')
                    closest_box = None

                    for box in boxes:
                        x1, y1, x2, y2 = box.cpu().numpy()
                        box_center = ((x1 + x2) / 2, (y1 + y2) / 2)
                        distance = np.sqrt((box_center[0] - frame_center[0])**2 + (box_center[1] - frame_center[1])**2)

                        if distance < min_distance:
                            min_distance = distance
                            closest_box = (x1, y1, x2, y2)

                    # 记录检测结果
                    self._log(f"YOLO detected {num_detections} targets, closest at distance {min_distance:.1f}")

                    # 这里可以添加瞄准逻辑
                    self._handle_target_detection(closest_box, min_distance)

        except Exception as e:
            self._log(f"YOLO processing error: {e}", "ERROR")

    def _handle_target_detection(self, box, distance):
        """处理目标检测结果"""
        try:
            aimbot_settings = self.config.get("aimbot_settings", {})
            aim_range = aimbot_settings.get("aim_range", 100)

            if distance < aim_range:
                # 目标在瞄准范围内
                x1, y1, x2, y2 = box
                target_center_x = (x1 + x2) / 2
                target_center_y = (y1 + y2) / 2

                # 计算偏移
                frame_center_x = 100  # 200/2
                frame_center_y = 100  # 200/2

                offset_x = target_center_x - frame_center_x
                offset_y = target_center_y - frame_center_y

                self._log(f"Target in range: offset({offset_x:.1f}, {offset_y:.1f})")

                # 这里可以添加鼠标移动逻辑
                # 由于是无头模式，我们只记录而不实际移动鼠标

        except Exception as e:
            self._log(f"Target handling error: {e}", "ERROR")

    def start_video_capture(self):
        """启动视频捕获"""
        if self.video_running:
            self._log("Video capture already running")
            return

        self._log("Starting video capture...")
        self.video_running = True

        # 启动视频捕获线程
        self.video_thread = threading.Thread(target=self._video_capture_loop, daemon=True)
        self.video_thread.start()

        self._log("Video capture started successfully")

    def stop_video_capture(self):
        """停止视频捕获"""
        if not self.video_running:
            return

        self._log("Stopping video capture...")
        self.video_running = False

        if self.video_thread:
            self.video_thread.join(timeout=5)

        self._log("Video capture stopped")

    def enable_yolo(self):
        """启用YOLO检测"""
        if not self.model:
            self._log("YOLO model not initialized, cannot enable detection", "ERROR")
            return False

        self.yolo_enabled = True
        self._log("YOLO detection enabled")
        return True

    def disable_yolo(self):
        """禁用YOLO检测"""
        self.yolo_enabled = False
        self._log("YOLO detection disabled")
    
    def _create_service_files(self):
        """创建服务相关文件"""
        service_files = {
            "graphics_driver_service.log": f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Graphics Driver Service started\n",
            "gpu_monitoring.dat": f"GPU_STATUS=ACTIVE\nLAST_CHECK={int(time.time())}\n",
            "display_config.ini": "[Display]\nResolution=1920x1080\nRefreshRate=144\nColorDepth=32\n",
            "service_status.tmp": f"SERVICE_PID={os.getpid()}\nSTATUS=RUNNING\nSTART_TIME={time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        }
        
        for filename, content in service_files.items():
            try:
                with open(filename, 'w') as f:
                    f.write(content)
            except:
                pass
    
    def _log(self, message, level="INFO"):
        """服务日志记录"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] [{level}] Graphics Service: {message}"
        print(log_message)
        
        # 写入日志文件
        try:
            with open("graphics_driver_service.log", 'a') as f:
                f.write(log_message + "\n")
        except:
            pass
    
    def _system_monitoring(self):
        """系统监控线程"""
        while self.running:
            try:
                # 模拟GPU监控
                gpu_usage = random.randint(10, 80)
                memory_usage = random.randint(20, 60)
                temperature = random.randint(45, 75)
                
                # 更新监控数据
                monitoring_data = f"GPU_USAGE={gpu_usage}%\nMEMORY_USAGE={memory_usage}%\nTEMPERATURE={temperature}C\nLAST_UPDATE={int(time.time())}\n"
                
                try:
                    with open("gpu_monitoring.dat", 'w') as f:
                        f.write(monitoring_data)
                except:
                    pass
                
                # 随机日志输出
                if random.random() < 0.1:  # 10% 概率
                    self._log(f"System monitoring - GPU: {gpu_usage}%, Memory: {memory_usage}%, Temp: {temperature}°C")
                
                # 模拟性能优化
                if random.random() < 0.05:  # 5% 概率
                    self._log("Performance optimization completed")
                
                # 等待下次检查
                time.sleep(self.config.get("monitoring_interval", 60))
                
            except Exception as e:
                self._log(f"Monitoring error: {e}", "ERROR")
                time.sleep(30)
    
    def _driver_maintenance(self):
        """驱动维护线程"""
        while self.running:
            try:
                # 模拟驱动维护任务
                maintenance_tasks = [
                    "Memory defragmentation",
                    "Cache optimization", 
                    "Registry cleanup",
                    "Driver integrity check",
                    "Hardware compatibility verification"
                ]
                
                # 随机执行维护任务
                if random.random() < 0.02:  # 2% 概率
                    task = random.choice(maintenance_tasks)
                    self._log(f"Executing maintenance task: {task}")
                    time.sleep(random.uniform(5, 15))
                    self._log(f"Maintenance task completed: {task}")
                
                time.sleep(random.uniform(300, 600))  # 5-10分钟间隔
                
            except Exception as e:
                self._log(f"Maintenance error: {e}", "ERROR")
                time.sleep(60)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self._log("Received shutdown signal")
        self.stop()
    
    def start(self):
        """启动后台服务"""
        self._log(f"Starting {self.service_name} v{self.version}")
        
        # 创建服务文件
        self._create_service_files()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.running = True
        
        # 启动监控线程
        monitoring_thread = threading.Thread(target=self._system_monitoring, daemon=True)
        monitoring_thread.start()
        
        # 启动维护线程
        maintenance_thread = threading.Thread(target=self._driver_maintenance, daemon=True)
        maintenance_thread.start()

        # 启动配置监控线程
        config_thread = threading.Thread(target=self._watch_config_changes, daemon=True)
        config_thread.start()

        # 初始化YOLO模型
        if self._initialize_yolo():
            self._log("YOLO model ready for detection")

            # 自动启动视频捕获
            self.start_video_capture()

            # 检查是否自动启用YOLO
            aimbot_settings = self.config.get("aimbot_settings", {})
            if aimbot_settings.get("enabled", True):
                self.enable_yolo()
                self._log("Auto-enabled YOLO detection based on configuration")
            else:
                self._log("YOLO detection disabled in configuration")
        else:
            self._log("YOLO initialization failed, running without detection", "WARNING")

        self._log("Background service started successfully")
        self._log("Service is running in stealth mode (no GUI)")
        self._log("Configuration monitoring enabled - edit hohoai_config.json to change settings")

        if self.video_running:
            self._log("Video capture is active")
        if self.yolo_enabled:
            self._log("YOLO detection is active")
        
        try:
            # 保持服务运行
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """停止后台服务"""
        self._log("Shutting down background service...")
        self.running = False

        # 停止视频捕获
        self.stop_video_capture()

        # 禁用YOLO
        self.disable_yolo()

        # 清理服务文件
        cleanup_files = [
            "service_status.tmp",
            "gpu_monitoring.dat"
        ]

        for filename in cleanup_files:
            try:
                if os.path.exists(filename):
                    os.remove(filename)
            except:
                pass

        self._log("Background service stopped")
        sys.exit(0)

def verify_card_key():
    """卡密验证"""
    print("Graphics Driver Service: Authentication required")

    # 卡密验证API
    CARD_API_URL = "http://************:5000/api/check_card"
    API_TOKEN = "3f8c1a9e4b9d63a01e6d2c14c956aa7b"

    for attempt in range(3):
        try:
            card_key = input(f"Enter license key (attempt {attempt+1}/3): ").strip()

            if not card_key:
                print("❌ License key cannot be empty")
                continue

            print("Graphics Service: Validating license...")

            # 发送验证请求
            response = requests.post(
                CARD_API_URL,
                json={"card_key": card_key},
                headers={"X-API-Token": API_TOKEN},
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("valid"):
                    print("✅ Graphics Service: License validation successful")
                    return True
                else:
                    print(f"❌ Graphics Service: License validation failed - {data.get('msg', 'Unknown error')}")
            else:
                print(f"❌ Graphics Service: Server error - {response.status_code}")

        except requests.exceptions.RequestException as e:
            print(f"❌ Graphics Service: Network error - {e}")
        except Exception as e:
            print(f"❌ Graphics Service: Validation error - {e}")

    print("❌ Graphics Service: License validation failed, service cannot start")
    return False

def start_headless_service():
    """启动无头模式服务"""
    print("Graphics Driver Service: Initializing headless mode...")

    # 检查是否跳过卡密验证（调试模式）
    skip_auth = os.environ.get('SKIP_LICENSE_CHECK', '').lower() == 'true'

    if skip_auth:
        print("🔧 Graphics Service: Debug mode - skipping license validation")
    else:
        # 卡密验证
        if not verify_card_key():
            print("Graphics Service: Unauthorized access denied")
            sys.exit(1)
        print("Graphics Service: Authorization confirmed, starting service...")

    service = GraphicsDriverBackgroundService()
    service.start()

if __name__ == "__main__":
    # 直接运行无头服务
    start_headless_service()
