import onnxruntime as ort

def simple_gpu_test():
    """简单的GPU测试"""
    
    print("ONNX Runtime版本:", ort.__version__)
    print("可用的执行提供者:", ort.get_available_providers())
    
    # 检查CUDA是否可用
    if 'CUDAExecutionProvider' in ort.get_available_providers():
        print("✅ CUDA执行提供者可用")
        return True
    else:
        print("❌ CUDA执行提供者不可用")
        print("\n需要安装:")
        print("1. CUDA 12.x")
        print("2. cuDNN 9.x") 
        print("3. onnxruntime-gpu")
        return False

def check_nvidia_driver():
    """检查NVIDIA驱动"""
    import subprocess
    
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=name,driver_version,cuda_version', '--format=csv'], 
                              capture_output=True, text=True, encoding='utf-8', errors='ignore')
        if result.returncode == 0:
            print("✅ NVIDIA驱动信息:")
            print(result.stdout)
            return True
        else:
            print("❌ 无法获取NVIDIA驱动信息")
            return False
    except:
        print("❌ nvidia-smi不可用")
        return False

if __name__ == "__main__":
    print("简单GPU环境检查")
    print("=" * 30)
    
    # 检查驱动
    driver_ok = check_nvidia_driver()
    print()
    
    # 检查ONNX Runtime
    gpu_ok = simple_gpu_test()
    
    print("\n" + "=" * 30)
    if driver_ok and gpu_ok:
        print("✅ GPU环境就绪")
    elif driver_ok and not gpu_ok:
        print("⚠️  驱动正常，但需要安装CUDA环境")
    else:
        print("❌ 需要安装NVIDIA驱动和CUDA环境")
