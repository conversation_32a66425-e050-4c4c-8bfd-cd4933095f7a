#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTorch 模型转 ONNX 格式转换器
支持多种模型类型和自定义输入尺寸
作者: AI Assistant
"""

import torch
import torch.onnx
import onnx
import onnxruntime as ort
import argparse
import os
import sys
from pathlib import Path
import numpy as np

def load_pytorch_model(model_path, device='cpu'):
    """
    加载 PyTorch 模型
    
    Args:
        model_path (str): 模型文件路径 (.pt 或 .pth)
        device (str): 设备类型 ('cpu' 或 'cuda')
    
    Returns:
        torch.nn.Module: 加载的模型
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        # 加载模型
        print(f"正在加载模型: {model_path}")
        model = torch.load(model_path, map_location=device)
        
        # 如果加载的是字典，尝试获取模型
        if isinstance(model, dict):
            if 'model' in model:
                model = model['model']
            elif 'state_dict' in model:
                print("检测到 state_dict，需要模型架构来加载")
                raise ValueError("需要提供模型架构或完整的模型文件")
        
        # 设置为评估模式
        model.eval()
        print(f"模型加载成功，设备: {device}")
        return model
        
    except Exception as e:
        print(f"加载模型失败: {e}")
        sys.exit(1)

def convert_to_onnx(model, input_shape, output_path, device='cpu', opset_version=11):
    """
    将 PyTorch 模型转换为 ONNX 格式
    
    Args:
        model: PyTorch 模型
        input_shape (tuple): 输入张量形状 (batch_size, channels, height, width)
        output_path (str): 输出 ONNX 文件路径
        device (str): 设备类型
        opset_version (int): ONNX opset 版本
    """
    try:
        # 创建虚拟输入
        dummy_input = torch.randn(input_shape).to(device)
        print(f"输入形状: {input_shape}")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        print("开始转换为 ONNX...")
        
        # 转换为 ONNX
        torch.onnx.export(
            model,                          # 模型
            dummy_input,                    # 模型输入
            output_path,                    # 输出文件路径
            export_params=True,             # 存储训练好的参数权重
            opset_version=opset_version,    # ONNX 版本
            do_constant_folding=True,       # 是否执行常量折叠优化
            input_names=['input'],          # 输入名称
            output_names=['output'],        # 输出名称
            dynamic_axes={                  # 动态轴
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            }
        )
        
        print(f"ONNX 模型保存成功: {output_path}")
        
    except Exception as e:
        print(f"转换失败: {e}")
        sys.exit(1)

def verify_onnx_model(onnx_path, input_shape):
    """
    验证 ONNX 模型
    
    Args:
        onnx_path (str): ONNX 模型路径
        input_shape (tuple): 输入形状
    """
    try:
        print("验证 ONNX 模型...")
        
        # 检查模型
        onnx_model = onnx.load(onnx_path)
        onnx.checker.check_model(onnx_model)
        print("✓ ONNX 模型格式验证通过")
        
        # 测试推理
        ort_session = ort.InferenceSession(onnx_path)
        
        # 创建测试输入
        test_input = np.random.randn(*input_shape).astype(np.float32)
        
        # 运行推理
        ort_inputs = {ort_session.get_inputs()[0].name: test_input}
        ort_outputs = ort_session.run(None, ort_inputs)
        
        print("✓ ONNX 模型推理测试通过")
        print(f"输入形状: {test_input.shape}")
        print(f"输出形状: {ort_outputs[0].shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ ONNX 模型验证失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='PyTorch 模型转 ONNX 格式转换器')
    parser.add_argument('--input', '-i', required=True, help='输入 PyTorch 模型路径 (.pt 或 .pth)')
    parser.add_argument('--output', '-o', help='输出 ONNX 模型路径 (默认: 输入文件名.onnx)')
    parser.add_argument('--input_shape', '-s', nargs=4, type=int, default=[1, 3, 640, 640],
                       help='输入张量形状 [batch_size, channels, height, width] (默认: 1 3 640 640)')
    parser.add_argument('--device', '-d', choices=['cpu', 'cuda'], default='cpu',
                       help='设备类型 (默认: cpu)')
    parser.add_argument('--opset', type=int, default=11,
                       help='ONNX opset 版本 (默认: 11)')
    parser.add_argument('--verify', action='store_true',
                       help='转换后验证 ONNX 模型')
    
    args = parser.parse_args()
    
    # 检查 CUDA 可用性
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("警告: CUDA 不可用，使用 CPU")
        args.device = 'cpu'
    
    # 设置输出路径
    if not args.output:
        input_path = Path(args.input)
        args.output = str(input_path.with_suffix('.onnx'))
    
    print("=" * 50)
    print("PyTorch 转 ONNX 转换器")
    print("=" * 50)
    print(f"输入模型: {args.input}")
    print(f"输出模型: {args.output}")
    print(f"输入形状: {args.input_shape}")
    print(f"设备: {args.device}")
    print(f"ONNX opset: {args.opset}")
    print("=" * 50)
    
    # 加载模型
    model = load_pytorch_model(args.input, args.device)
    
    # 转换为 ONNX
    convert_to_onnx(model, tuple(args.input_shape), args.output, args.device, args.opset)
    
    # 验证模型
    if args.verify:
        verify_onnx_model(args.output, tuple(args.input_shape))
    
    print("=" * 50)
    print("转换完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
