# KMBox 硬件鼠标使用说明

## 🎮 什么是 KMBox？

KMBox 是一个硬件鼠标模拟器，通过网络连接控制鼠标移动，比软件移动更安全、更难检测。

## 🔧 KMBox 配置步骤

### 1. 运行 KMBox 测试工具
```bash
python kmbox_test.py
```

### 2. 配置 KMBox 参数
选择 "2. 配置KMBox参数"，输入您的KMBox设备信息：

```
IP地址: *************    # KMBox设备的IP地址
端口: 8888               # 通常是8888
UUID: 6EC5D1A1          # 您的KMBox设备UUID
启用KMBox: y            # 启用KMBox功能
```

### 3. 测试连接
选择 "1. 测试KMBox连接" 验证设备是否正常工作：

```
🔧 正在测试KMBox连接...
   IP: *************
   端口: 8888
   UUID: 6EC5D1A1
✅ KMBox模块加载成功
🔌 正在连接KMBox...
✅ KMBox连接成功！
📍 当前鼠标位置: (960, 540)
🖱️ 执行移动测试...
   测试 1/4: 移动 (+10, +0) ✅
   测试 2/4: 移动 (+0, +10) ✅
   测试 3/4: 移动 (-10, +0) ✅
   测试 4/4: 移动 (+0, -10) ✅
✅ KMBox移动测试完成
```

## ⚙️ 配置文件设置

成功配置后，`hohoai_config.json` 会包含：

```json
{
  "kmbox_settings": {
    "enabled": true,              // 启用KMBox
    "ip": "*************",       // KMBox IP地址
    "port": 8888,                // 端口
    "uuid": "6EC5D1A1",          // 设备UUID
    "test_on_startup": true      // 启动时测试连接
  }
}
```

## 🚀 启动带 KMBox 的服务

配置完成后，重新启动服务：

```bash
python start_service.py --skip-auth
```

## 📊 KMBox 工作日志

启动时会看到：

```
[INFO] Graphics Service: KMBox module found, attempting to initialize...
[INFO] Graphics Service: Attempting to connect to KMBox at *************:8888
[INFO] Graphics Service: ✅ KMBox connected successfully!
[INFO] Graphics Service: 🔧 Testing KMBox connection...
[INFO] Graphics Service: 📍 Current mouse position: (960, 540)
[INFO] Graphics Service: 🖱️ KMBox test move: (5, 5)
[INFO] Graphics Service: ✅ KMBox movement test completed successfully
```

检测到目标时：

```
[INFO] Graphics Service: 🎯 检测到 1 个目标，最近距离 45.3 像素
[INFO] Graphics Service: 🎯 目标锁定！距离: 45.3px, 偏移: (-38.2, -12.1), 大小: 32x58
[INFO] Graphics Service: 🎮 瞄准状态: 中等精度 | 建议移动: X-38.2, Y-12.1
[INFO] Graphics Service: 🎮 KMBox瞄准: 移动 (-15.3, -3.6) | 中等精度  ← KMBox移动！
```

## 🔄 软件 vs KMBox 对比

| 特性 | 软件移动 | KMBox硬件 |
|------|----------|-----------|
| 检测难度 | 容易被检测 | 极难检测 |
| 移动精度 | 一般 | 高精度 |
| 延迟 | 较高 | 极低 |
| 成本 | 免费 | 需要硬件 |
| 安全性 | 中等 | 极高 |

## 🛠️ 故障排除

### KMBox连接失败
```
❌ KMBox连接失败，错误代码: -1
```

**解决方法：**
1. 检查IP地址是否正确
2. 确认KMBox设备已连接并启动
3. 检查网络连接
4. 验证UUID是否匹配

### 模块导入失败
```
❌ KMBox模块未找到
```

**解决方法：**
1. 确保 `Module/control.py` 文件存在
2. 检查KMBox驱动是否正确安装

### 移动测试失败
```
⚠️ 获取鼠标位置失败
```

**解决方法：**
1. 重启KMBox设备
2. 检查USB连接
3. 重新配置网络设置

## 🎯 使用建议

### 1. 首次使用
- 先用软件模式测试功能
- 确认检测正常后再配置KMBox
- 逐步调整参数

### 2. 参数优化
```json
{
  "aimbot_settings": {
    "aim_speed_x": 0.3,    // KMBox可以用更高的速度
    "aim_speed_y": 0.2,    // 因为硬件移动更精确
    "aim_range": 120       // 可以扩大瞄准范围
  }
}
```

### 3. 网络设置
- 确保KMBox和电脑在同一网络
- 使用有线连接获得最佳性能
- 避免网络延迟影响

## 📋 快速操作指南

### 启用KMBox
```bash
python kmbox_test.py
# 选择 4 → 启用KMBox
```

### 测试连接
```bash
python kmbox_test.py
# 选择 1 → 测试连接
```

### 查看状态
```bash
python kmbox_test.py
# 选择 3 → 查看配置
```

## 🎮 完整工作流程

1. **配置KMBox** → `python kmbox_test.py`
2. **测试连接** → 确认硬件正常
3. **启动服务** → `python start_service.py --skip-auth`
4. **验证日志** → 看到 "🎮 KMBox瞄准" 表示成功
5. **调整参数** → 根据效果微调配置

现在您可以使用更安全的KMBox硬件进行鼠标控制了！🎯🎮
