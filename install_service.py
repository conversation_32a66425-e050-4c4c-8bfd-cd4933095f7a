#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows服务安装器
将HohoAI安装为Windows系统服务
"""

import os
import sys
import subprocess
import ctypes

def is_admin():
    """检查是否以管理员身份运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def create_service_script():
    """创建Windows服务脚本"""
    service_code = '''
import win32serviceutil
import win32service
import win32event
import servicemanager
import sys
import os
import time
import threading

class NVIDIADisplayService(win32serviceutil.ServiceFramework):
    _svc_name_ = "NVDisplayContainer"
    _svc_display_name_ = "NVIDIA Display Container LS"
    _svc_description_ = "NVIDIA Display Container Local System Service"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.running = True

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        self.running = False
        win32event.SetEvent(self.hWaitStop)

    def SvcDoRun(self):
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                              servicemanager.PYS_SERVICE_STARTED,
                              (self._svc_name_, ''))
        self.main()

    def main(self):
        """服务主函数"""
        try:
            # 切换到脚本目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            os.chdir(script_dir)
            
            # 启动无头服务
            from headless_service import GraphicsDriverBackgroundService
            
            service = GraphicsDriverBackgroundService()
            
            # 在单独线程中运行服务
            def run_service():
                try:
                    service.start()
                except Exception as e:
                    servicemanager.LogErrorMsg(f"Service error: {e}")
            
            service_thread = threading.Thread(target=run_service, daemon=True)
            service_thread.start()
            
            # 等待停止信号
            while self.running:
                time.sleep(1)
                
        except Exception as e:
            servicemanager.LogErrorMsg(f"Service startup error: {e}")

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(NVIDIADisplayService)
'''
    
    with open("nvidia_display_service.py", "w") as f:
        f.write(service_code)
    
    print("✅ Windows服务脚本已创建: nvidia_display_service.py")

def install_service():
    """安装Windows服务"""
    if not is_admin():
        print("❌ 需要管理员权限来安装Windows服务")
        print("请以管理员身份重新运行此脚本")
        return False
    
    try:
        # 安装必要的依赖
        print("📦 安装Windows服务依赖...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pywin32"], check=True)
        
        # 创建服务脚本
        create_service_script()
        
        # 安装服务
        print("🔧 安装Windows服务...")
        subprocess.run([sys.executable, "nvidia_display_service.py", "install"], check=True)
        
        # 启动服务
        print("🚀 启动Windows服务...")
        subprocess.run([sys.executable, "nvidia_display_service.py", "start"], check=True)
        
        print("✅ Windows服务安装成功！")
        print("服务名称: NVIDIA Display Container LS")
        print("服务将在系统启动时自动运行")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 服务安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def uninstall_service():
    """卸载Windows服务"""
    if not is_admin():
        print("❌ 需要管理员权限来卸载Windows服务")
        return False
    
    try:
        print("🛑 停止并卸载Windows服务...")
        subprocess.run([sys.executable, "nvidia_display_service.py", "stop"], check=False)
        subprocess.run([sys.executable, "nvidia_display_service.py", "remove"], check=True)
        
        print("✅ Windows服务已卸载")
        return True
        
    except Exception as e:
        print(f"❌ 卸载失败: {e}")
        return False

def main():
    """主函数"""
    print("🛡️ HohoAI Windows服务安装器")
    print("将程序安装为系统服务，完全隐藏进程")
    
    while True:
        print("\n" + "="*50)
        print("选择操作:")
        print("1. 安装为Windows服务")
        print("2. 卸载Windows服务")
        print("3. 创建伪装启动器")
        print("0. 退出")
        
        choice = input("\n请选择 (0-3): ").strip()
        
        if choice == "0":
            break
        elif choice == "1":
            install_service()
        elif choice == "2":
            uninstall_service()
        elif choice == "3":
            create_service_script()
            print("✅ 伪装启动器已创建")
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
