#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HohoAI 隐蔽启动器
提供多种反检测运行模式
"""

import os
import sys
import time
import subprocess
import argparse
import ctypes

def set_console_title(title):
    """设置控制台标题"""
    try:
        ctypes.windll.kernel32.SetConsoleTitleW(title)
    except:
        pass

def print_banner():
    """显示启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                Graphics Driver Service Manager               ║
    ║                      v27.20.100.8681                        ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  反检测运行模式选择器                                        ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def show_modes():
    """显示可用的运行模式"""
    modes = """
    可用的运行模式：
    
    1. 🔒 完全隐蔽模式 (推荐)
       - 无GUI界面
       - 后台服务运行
       - 最高反检测等级
       
    2. 🎯 混合模式
       - 最小化GUI
       - 隐藏到系统托盘
       - 中等反检测等级
       
    3. 🖥️ 标准模式
       - 完整GUI界面
       - 正常功能
       - 基础反检测
       
    4. ⚙️ 服务模式
       - Windows服务安装
       - 开机自启动
       - 完全后台运行
    """
    print(modes)

def run_stealth_mode():
    """运行完全隐蔽模式"""
    print("🔒 启动完全隐蔽模式...")
    set_console_title("Graphics Driver Background Service")
    
    # 设置环境变量
    os.environ['GRAPHICS_SERVICE_MODE'] = 'stealth'
    
    try:
        # 启动无头服务
        subprocess.run([sys.executable, "headless_service.py"], check=True)
    except FileNotFoundError:
        print("❌ 无头服务文件未找到，启动最小化模式...")
        subprocess.run([sys.executable, "hohoai.py", "--stealth"], check=True)

def run_hybrid_mode():
    """运行混合模式"""
    print("🎯 启动混合模式...")
    set_console_title("Graphics Driver Service")
    
    # 设置环境变量
    os.environ['GRAPHICS_SERVICE_MODE'] = 'hybrid'
    
    # 启动主程序（最小化GUI）
    subprocess.run([sys.executable, "hohoai.py", "--minimized"], check=True)

def run_standard_mode():
    """运行标准模式"""
    print("🖥️ 启动标准模式...")
    set_console_title("Graphics Driver Service")
    
    # 启动主程序（完整GUI）
    subprocess.run([sys.executable, "hohoai.py"], check=True)

def install_service():
    """安装为Windows服务"""
    print("⚙️ 安装Windows服务...")
    
    service_script = """
import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import os

class GraphicsDriverService(win32serviceutil.ServiceFramework):
    _svc_name_ = "GraphicsDriverService"
    _svc_display_name_ = "Graphics Driver Service"
    _svc_description_ = "NVIDIA Graphics Driver Background Service"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)

    def SvcDoRun(self):
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                              servicemanager.PYS_SERVICE_STARTED,
                              (self._svc_name_, ''))
        self.main()

    def main(self):
        # 启动无头服务
        import subprocess
        import sys
        script_dir = os.path.dirname(os.path.abspath(__file__))
        subprocess.run([sys.executable, os.path.join(script_dir, "headless_service.py")])

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(GraphicsDriverService)
"""
    
    # 保存服务脚本
    with open("graphics_service.py", "w") as f:
        f.write(service_script)
    
    print("✅ 服务脚本已创建")
    print("📝 请以管理员身份运行以下命令安装服务：")
    print("   python graphics_service.py install")
    print("   python graphics_service.py start")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="HohoAI 隐蔽启动器")
    parser.add_argument("--mode", choices=["stealth", "hybrid", "standard", "service"], 
                       help="运行模式")
    parser.add_argument("--auto", action="store_true", help="自动选择最佳模式")
    
    args = parser.parse_args()
    
    set_console_title("Graphics Driver Service Manager")
    print_banner()
    
    if args.auto:
        print("🤖 自动选择最佳运行模式...")
        # 检查是否在游戏环境中
        if os.path.exists("C:\\Program Files\\Common Files\\BattlEye") or \
           os.path.exists("C:\\Program Files (x86)\\Common Files\\BattlEye"):
            print("🔍 检测到反作弊环境，使用完全隐蔽模式")
            run_stealth_mode()
        else:
            print("🎯 使用混合模式")
            run_hybrid_mode()
        return
    
    if args.mode:
        if args.mode == "stealth":
            run_stealth_mode()
        elif args.mode == "hybrid":
            run_hybrid_mode()
        elif args.mode == "standard":
            run_standard_mode()
        elif args.mode == "service":
            install_service()
        return
    
    # 交互式模式选择
    show_modes()
    
    while True:
        try:
            choice = input("\n请选择运行模式 (1-4): ").strip()
            
            if choice == "1":
                run_stealth_mode()
                break
            elif choice == "2":
                run_hybrid_mode()
                break
            elif choice == "3":
                run_standard_mode()
                break
            elif choice == "4":
                install_service()
                break
            else:
                print("❌ 无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n👋 退出启动器")
            sys.exit(0)
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
