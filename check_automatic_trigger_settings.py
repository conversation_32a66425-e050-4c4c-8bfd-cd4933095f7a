#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查自動扳機設置和進程狀態
"""

import sys
import os
import time
import win32api
import multiprocessing
from multiprocessing import Process, Queue

# 添加當前目錄到Python路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from Module import config, logger
except ImportError as e:
    print(f"無法導入模組: {e}")
    print("請確保在正確的目錄中運行此腳本")
    sys.exit(1)

def check_config_values():
    """檢查配置文件中的自動扳機設置"""
    print("=== 檢查配置文件設置 ===\n")
    
    try:
        # 讀取配置文件
        config_values = config.read_config()
        
        print("當前配置文件設置:")
        print(f"  自動扳機開關: {config_values.get('automatic_trigger_switch', '未設置')}")
        print(f"  自動扳機範圍: {config_values.get('automatic_trigger_range', '未設置')}")
        print(f"  觸發形式: {config_values.get('automatic_trigger_mode', '未設置')}")
        print(f"  生效模式: {config_values.get('Effective_mode', '未設置')}")
        print(f"  點擊模式: {config_values.get('click_mode', '未設置')}")
        print(f"  滑鼠移動模式: {config_values.get('mouseMoveMode', '未設置')}")
        
        return config_values
        
    except Exception as e:
        print(f"讀取配置文件失敗: {e}")
        return None

def check_wasd_detection():
    """檢查WASD按鍵檢測"""
    print("\n=== 檢查WASD按鍵檢測 ===\n")
    
    print("請按住WASD任意鍵，觀察檢測結果...")
    print("按Ctrl+C停止檢測\n")
    
    try:
        while True:
            # 檢測WASD按鍵
            state_w = bool(win32api.GetAsyncKeyState(0x57) & 0x8000)  # W键
            state_a = bool(win32api.GetAsyncKeyState(0x41) & 0x8000)  # A键
            state_s = bool(win32api.GetAsyncKeyState(0x53) & 0x8000)  # S键
            state_d = bool(win32api.GetAsyncKeyState(0x44) & 0x8000)  # D键
            
            wasd_pressed = state_w or state_a or state_s or state_d
            
            status = f"[{time.strftime('%H:%M:%S')}] WASD狀態: {'按下' if wasd_pressed else '鬆開'} (W:{state_w}, A:{state_a}, S:{state_s}, D:{state_d})"
            print(f"\r{status}", end="", flush=True)
            
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n\nWASD檢測停止")

def check_process_status():
    """檢查進程狀態"""
    print("\n=== 檢查進程狀態 ===\n")
    
    # 檢查是否有Python進程在運行
    import psutil
    
    python_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'hohoai.py' in cmdline:
                    python_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if python_processes:
        print("找到運行中的Python進程:")
        for proc in python_processes:
            print(f"  PID: {proc['pid']}")
            print(f"  名稱: {proc['name']}")
            print(f"  命令行: {proc['cmdline']}")
            print()
    else:
        print("未找到運行中的hohoai.py進程")
        print("請確保主程序正在運行")

def check_log_output():
    """檢查日誌輸出"""
    print("\n=== 檢查日誌輸出 ===\n")
    
    log_file = "app.log"
    if os.path.exists(log_file):
        print(f"找到日誌文件: {log_file}")
        
        # 讀取最後20行日誌
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                last_lines = lines[-20:] if len(lines) > 20 else lines
                
            print("最後20行日誌:")
            for line in last_lines:
                print(f"  {line.strip()}")
                
        except Exception as e:
            print(f"讀取日誌文件失敗: {e}")
    else:
        print("未找到日誌文件")
        print("請確保程序已經運行過並生成了日誌")

def check_automatic_trigger_logic():
    """檢查自動扳機邏輯"""
    print("\n=== 檢查自動扳機邏輯 ===\n")
    
    # 模擬自動扳機邏輯
    automatic_trigger_switch = True
    Trigger_conditions = True
    mouse_isdown = True
    wasd_cooldown_time = 0.3
    last_wasd_movement_time = 0
    wasd_movement_detected = False
    
    print("模擬自動扳機邏輯:")
    print(f"  自動扳機開關: {automatic_trigger_switch}")
    print(f"  觸發條件: {Trigger_conditions}")
    print(f"  滑鼠按下: {mouse_isdown}")
    print(f"  冷卻時間: {wasd_cooldown_time}秒")
    print()
    
    print("請按住WASD測試邏輯...")
    print("按Ctrl+C停止\n")
    
    try:
        while True:
            current_time = time.time()
            
            # 檢測WASD按鍵
            state_w = bool(win32api.GetAsyncKeyState(0x57) & 0x8000)
            state_a = bool(win32api.GetAsyncKeyState(0x41) & 0x8000)
            state_s = bool(win32api.GetAsyncKeyState(0x53) & 0x8000)
            state_d = bool(win32api.GetAsyncKeyState(0x44) & 0x8000)
            
            wasd_pressed = state_w or state_a or state_s or state_d
            
            # 檢測WASD移動開始
            if wasd_pressed and not wasd_movement_detected:
                last_wasd_movement_time = current_time
                wasd_movement_detected = True
                print(f"[{time.strftime('%H:%M:%S')}] 🔥 檢測到WASD按下！")
            
            # 重置檢測狀態
            if not wasd_pressed:
                wasd_movement_detected = False
            
            # 檢查是否在冷卻時間內
            in_cooldown = (current_time - last_wasd_movement_time) < wasd_cooldown_time and not wasd_pressed
            
            # 檢查自動扳機條件
            will_trigger = (automatic_trigger_switch and 
                           Trigger_conditions and 
                           mouse_isdown and 
                           not in_cooldown and 
                           not wasd_pressed)
            
            # 顯示狀態
            status_parts = []
            status_parts.append(f"WASD: {'按下' if wasd_pressed else '鬆開'}")
            status_parts.append(f"冷卻: {'是' if in_cooldown else '否'}")
            status_parts.append(f"自動扳機: {'開啟' if will_trigger else '關閉'}")
            
            if wasd_pressed:
                status_parts.append("原因: WASD按下")
            elif in_cooldown:
                remaining = wasd_cooldown_time - (current_time - last_wasd_movement_time)
                status_parts.append(f"原因: 冷卻中({remaining:.2f}s)")
            elif will_trigger:
                status_parts.append("原因: 所有條件滿足")
            else:
                status_parts.append("原因: 其他條件不滿足")
            
            status_line = f"[{time.strftime('%H:%M:%S')}] {' | '.join(status_parts)}"
            print(f"\r{status_line}", end="", flush=True)
            
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n\n邏輯測試停止")

def main():
    print("自動扳機設置檢查工具\n")
    print("=" * 60)
    
    print("選擇檢查項目:")
    print("1. 檢查配置文件設置")
    print("2. 檢查WASD按鍵檢測")
    print("3. 檢查進程狀態")
    print("4. 檢查日誌輸出")
    print("5. 檢查自動扳機邏輯")
    print("6. 全部檢查")
    print("7. 退出")
    
    while True:
        try:
            choice = input("\n請選擇 (1-7): ").strip()
            
            if choice == "1":
                check_config_values()
                break
            elif choice == "2":
                check_wasd_detection()
                break
            elif choice == "3":
                check_process_status()
                break
            elif choice == "4":
                check_log_output()
                break
            elif choice == "5":
                check_automatic_trigger_logic()
                break
            elif choice == "6":
                check_config_values()
                check_wasd_detection()
                check_process_status()
                check_log_output()
                check_automatic_trigger_logic()
                break
            elif choice == "7":
                print("退出檢查")
                break
            else:
                print("無效選擇，請輸入1-7")
                
        except KeyboardInterrupt:
            print("\n退出檢查")
            break

if __name__ == "__main__":
    main() 