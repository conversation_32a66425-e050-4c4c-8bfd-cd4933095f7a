#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查WASD冷卻功能的日誌輸出
"""

def check_expected_logs():
    """檢查應該看到的日誌模式"""
    print("=== WASD冷卻功能日誌檢查 ===\n")
    
    print("應該在日誌中看到的WASD冷卻相關信息:")
    print()
    print("1. WASD移動檢測:")
    print("   [DEBUG]檢測到WASD移動，開始冷卻計時: 0.3秒")
    print()
    print("2. 自動扳機被WASD冷卻阻止:")
    print("   [DEBUG]自动扳机被WASD冷卻阻止 - 开关: True, 条件: True, 鼠标按下: True, WASD冷卻: True")
    print()
    print("3. 自動扳機正常觸發:")
    print("   [DEBUG]自动扳机触发 - 开关: True, 条件: True, 鼠标按下: True, WASD冷卻: False")
    print()
    print("4. 自動扳機未觸發（其他原因）:")
    print("   [DEBUG]自动扳机未触发 - 开关: True, 条件: False, 鼠标按下: True, WASD冷卻: False")
    print()
    
    print("如果沒有看到這些日誌，可能的原因:")
    print("1. 自動扳機開關沒有開啟")
    print("2. 沒有檢測到目標（Trigger_conditions為False）")
    print("3. 滑鼠沒有按下（mouse_isdown為False）")
    print("4. 進程通信問題")
    print("5. Logger級別設置問題")
    print()
    
    print("調試步驟:")
    print("1. 確認自動扳機開關已開啟")
    print("2. 確認有目標被檢測到")
    print("3. 確認滑鼠按下（或使用持續生效模式）")
    print("4. 按下WASD按鍵，觀察日誌輸出")
    print("5. 等待0.3秒後，觀察自動扳機是否重新啟用")

def simulate_wasd_cooldown_logic():
    """模擬WASD冷卻邏輯"""
    print("=== WASD冷卻邏輯模擬 ===\n")
    
    scenarios = [
        {
            "time": 0.0,
            "wasd_pressed": True,
            "automatic_trigger_switch": True,
            "Trigger_conditions": True,
            "mouse_isdown": True,
            "description": "按下WASD，開始冷卻"
        },
        {
            "time": 0.1,
            "wasd_pressed": False,
            "automatic_trigger_switch": True,
            "Trigger_conditions": True,
            "mouse_isdown": True,
            "description": "WASD鬆開，仍在冷卻中"
        },
        {
            "time": 0.4,
            "wasd_pressed": False,
            "automatic_trigger_switch": True,
            "Trigger_conditions": True,
            "mouse_isdown": True,
            "description": "冷卻結束，自動扳機可用"
        }
    ]
    
    wasd_cooldown_time = 0.3
    last_wasd_movement_time = 0
    wasd_movement_detected = False
    
    for scenario in scenarios:
        current_time = scenario["time"]
        
        # 檢測WASD移動開始
        if scenario["wasd_pressed"] and not wasd_movement_detected:
            last_wasd_movement_time = current_time
            wasd_movement_detected = True
        
        # 重置檢測狀態
        if not scenario["wasd_pressed"]:
            wasd_movement_detected = False
        
        # 檢查冷卻狀態
        in_cooldown = (current_time - last_wasd_movement_time) < wasd_cooldown_time
        
        # 檢查自動扳機條件
        will_trigger = (scenario["automatic_trigger_switch"] and 
                       scenario["Trigger_conditions"] and 
                       scenario["mouse_isdown"] and 
                       not in_cooldown)
        
        print(f"時間: {scenario['time']:.1f}s - {scenario['description']}")
        print(f"  WASD按下: {scenario['wasd_pressed']}")
        print(f"  自動扳機開關: {scenario['automatic_trigger_switch']}")
        print(f"  觸發條件: {scenario['Trigger_conditions']}")
        print(f"  滑鼠按下: {scenario['mouse_isdown']}")
        print(f"  WASD冷卻: {in_cooldown}")
        print(f"  自動扳機結果: {'觸發' if will_trigger else '不觸發'}")
        print()

def main():
    print("WASD冷卻功能調試工具\n")
    print("=" * 50)
    
    simulate_wasd_cooldown_logic()
    check_expected_logs()
    
    print("=" * 50)
    print("調試完成")
    print("\n請檢查程序日誌，確認是否看到WASD冷卻相關的調試信息")

if __name__ == "__main__":
    main() 