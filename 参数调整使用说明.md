# HohoAI 参数调整使用说明

## 🎯 新的调参流程

### 1. 启动无头服务
```bash
python headless_service.py
```
服务会在后台运行，显示：
```
Graphics Driver Service: Initializing headless mode...
[2025-08-03 23:40:07] [INFO] Graphics Service: Starting Graphics Driver Background Service v27.20.100.8681  
[2025-08-03 23:40:07] [INFO] Graphics Service: Background service started successfully
[2025-08-03 23:40:07] [INFO] Graphics Service: Service is running in stealth mode (no GUI)
```

### 2. 运行参数调整工具
```bash
python adjust_params.py
```

### 3. 卡密验证
程序会提示输入卡密：
```
🎮 HohoAI 参数调整工具
Graphics Driver Service Configuration Tool
请输入卡密 (第1/3次): 
```

### 4. 自动打开配置文件
卡密验证成功后，程序会：
- ✅ 自动打开 `hohoai_config.json` 配置文件
- 💡 显示使用说明
- 🔄 等待您编辑完成

## 📝 配置文件参数说明

### 瞄准设置 (aimbot_settings)
```json
{
  "enabled": true,           // 启用/禁用自瞄 (true/false)
  "aim_speed_x": 0.2,       // X轴瞄准速度 (0.1-2.0)
  "aim_speed_y": 0.0,       // Y轴瞄准速度 (0.0-2.0)
  "aim_range": 100,         // 瞄准范围 (50-300)
  "offset_centerx": 0,      // X轴偏移 (-1.0 到 1.0)
  "offset_centery": 0.3,    // Y轴偏移 (-1.0 到 1.0)
  "yolo_confidence": 0.5,   // 检测置信度 (0.1-0.9)
  "target_class": "ALL"     // 目标类别 ("ALL", "0", "1")
}
```

### 高级设置 (advanced_settings)
```json
{
  "dynamic_aim_range_enabled": true,  // 动态瞄准范围 (true/false)
  "target_aim_range_scale": 0.6,     // 精确模式缩放比例 (0.1-1.0)
  "smooth_aiming": false,             // 平滑瞄准 (true/false)
  "recoil_suppression_switch": false, // 压枪辅助 (true/false)
  "recoil_strength": 50,              // 压枪强度 (0-100)
  "auto_robot_enabled": false         // 自动机器人 (true/false)
}
```

### 显示设置 (display_settings)
```json
{
  "screen_pixels_for_360_degrees": 1800,  // 游戏内360度像素数
  "screen_height_pixels": 900,            // 游戏内Y轴像素数
  "near_speed_multiplier": 2,             // 近点速度倍率
  "slow_zone_radius": 10                  // 减速区域半径
}
```

## 🔄 实时应用机制

1. **编辑配置文件** - 修改 `hohoai_config.json` 中的参数
2. **保存文件** - Ctrl+S 保存
3. **自动应用** - 无头服务每5秒检查一次文件变化
4. **立即生效** - 新参数会自动应用到运行中的程序

## 📊 常用参数调整示例

### 提高瞄准速度
```json
"aim_speed_x": 0.5,  // 从 0.2 改为 0.5
"aim_speed_y": 0.2   // 从 0.0 改为 0.2
```

### 扩大瞄准范围
```json
"aim_range": 150     // 从 100 改为 150
```

### 启用平滑瞄准
```json
"smooth_aiming": true  // 从 false 改为 true
```

### 调整检测精度
```json
"yolo_confidence": 0.6,  // 提高置信度
"target_class": "0"      // 只检测人物
```

### 启用压枪辅助
```json
"recoil_suppression_switch": true,  // 启用压枪
"recoil_strength": 60               // 设置压枪强度
```

## 🎮 使用技巧

### 1. 快速调参
- 保持配置文件打开
- 修改参数后立即保存
- 观察游戏中的效果
- 继续微调直到满意

### 2. 参数备份
```bash
# 备份当前配置
copy hohoai_config.json hohoai_config_backup.json
```

### 3. 恢复默认设置
删除 `hohoai_config.json`，重新运行调参工具会创建默认配置。

## 🔍 故障排除

### 配置文件不生效
1. 检查JSON格式是否正确
2. 查看无头服务日志：`tail -f graphics_driver_service.log`
3. 确认文件保存成功

### 参数超出范围
- 瞄准速度：0.1-2.0
- 瞄准范围：50-300
- 偏移量：-1.0到1.0
- 置信度：0.1-0.9

### 卡密验证失败
- 检查网络连接
- 确认卡密正确
- 联系管理员

## 💡 最佳实践

1. **小幅调整** - 每次只调整一个参数
2. **测试验证** - 调整后在游戏中测试效果
3. **记录设置** - 记录好用的参数组合
4. **定期备份** - 备份有效的配置文件

## 🎯 总结

新的调参流程：
1. 运行 `python adjust_params.py`
2. 输入卡密验证
3. 自动打开配置文件
4. 编辑参数并保存
5. 无头服务自动应用新设置

这样既保持了完全隐蔽模式，又提供了便捷的参数调整方式！
