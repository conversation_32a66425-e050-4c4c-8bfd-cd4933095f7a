import onnxruntime as ort
import numpy as np

def test_gpu_availability():
    """测试GPU是否可用"""
    
    print("ONNX Runtime版本:", ort.__version__)
    print("可用的执行提供者:", ort.get_available_providers())
    
    # 检查CUDA是否可用
    if 'CUDAExecutionProvider' in ort.get_available_providers():
        print("✅ CUDA执行提供者可用")
        
        try:
            # 创建一个简单的测试会话
            providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
            
            # 创建一个简单的ONNX模型进行测试
            import torch
            import torch.onnx
            
            # 创建简单模型
            class SimpleModel(torch.nn.Module):
                def forward(self, x):
                    return x * 2
            
            model = SimpleModel()
            dummy_input = torch.randn(1, 3, 224, 224)
            
            # 导出为ONNX
            torch.onnx.export(model, dummy_input, "test_model.onnx", 
                            export_params=True, opset_version=11)
            
            # 测试GPU推理
            session = ort.InferenceSession("test_model.onnx", providers=providers)
            
            print("✅ GPU推理会话创建成功")
            print("使用的执行提供者:", session.get_providers())
            
            # 测试推理
            input_data = np.random.randn(1, 3, 224, 224).astype(np.float32)
            outputs = session.run(None, {'input': input_data})
            
            print("✅ GPU推理测试成功")
            
            # 清理测试文件
            import os
            if os.path.exists("test_model.onnx"):
                os.remove("test_model.onnx")
                
            return True
            
        except Exception as e:
            print(f"❌ GPU推理测试失败: {e}")
            return False
    else:
        print("❌ CUDA执行提供者不可用")
        print("请检查CUDA和cuDNN安装")
        return False

def check_cuda_installation():
    """检查CUDA安装"""
    import subprocess
    import os

    print("\n检查CUDA安装:")

    # 检查nvidia-smi
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, encoding='utf-8', errors='ignore')
        if result.returncode == 0 and result.stdout:
            print("✅ NVIDIA驱动已安装")
            print("GPU信息:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'CUDA Version' in line:
                    print(f"  {line.strip()}")
        else:
            print("❌ nvidia-smi命令失败或无输出")
    except FileNotFoundError:
        print("❌ 未找到nvidia-smi，请安装NVIDIA驱动")
    except Exception as e:
        print(f"❌ 检查nvidia-smi时出错: {e}")
    
    # 检查CUDA路径
    cuda_path = os.environ.get('CUDA_PATH')
    if cuda_path:
        print(f"✅ CUDA_PATH: {cuda_path}")
    else:
        print("❌ 未设置CUDA_PATH环境变量")
    
    # 检查PATH中的CUDA
    path_dirs = os.environ.get('PATH', '').split(';')
    cuda_in_path = any('cuda' in dir.lower() for dir in path_dirs)
    if cuda_in_path:
        print("✅ CUDA在PATH中")
    else:
        print("❌ CUDA不在PATH中")

if __name__ == "__main__":
    print("GPU环境测试")
    print("=" * 50)
    
    check_cuda_installation()
    print("\n" + "=" * 50)
    test_gpu_availability()
