# HohoAI 检测日志示例

## 🎯 是的！检测到人会显示详细信息

当您运行 `python start_service.py --skip-auth` 后，系统会实时显示检测结果：

## 📊 检测到目标时的日志

### 🎯 基本检测信息
```
[INFO] Graphics Service: 🎯 检测到 2 个人物，最近距离 45.3 像素
[INFO] Graphics Service: 🎯 目标锁定！距离: 45.3px, 偏移: (12.5, -8.2), 大小: 35x67
[INFO] Graphics Service: 🎮 瞄准状态: 精确瞄准 | 建议移动: X+12.5, Y-8.2
```

### 📹 视频处理状态
```
[INFO] Graphics Service: 📹 视频处理: 100 帧
[INFO] Graphics Service: 📊 检测统计: 检测率 15.2% | 平均目标数 1.3 | 上次检测 2.1秒前
```

### 🔍 扫描状态
```
[INFO] Graphics Service: 🔍 扫描中... 暂未发现目标
[INFO] Graphics Service: 👁️ 发现目标但超出范围: 距离 156.7px > 100px
```

## 🎮 不同瞄准状态的显示

### ✅ 精确瞄准（距离 < 30% 瞄准范围）
```
[INFO] Graphics Service: 🎯 目标锁定！距离: 25.8px, 偏移: (5.2, -3.1), 大小: 42x78
[INFO] Graphics Service: 🎮 瞄准状态: 精确瞄准 | 建议移动: X+5.2, Y-3.1
```

### 🟡 中等精度（距离 30-60% 瞄准范围）
```
[INFO] Graphics Service: 🎯 目标锁定！距离: 55.4px, 偏移: (18.7, -12.3), 大小: 38x65
[INFO] Graphics Service: 🎮 瞄准状态: 中等精度 | 建议移动: X+18.7, Y-12.3
```

### 🟠 粗略瞄准（距离 60-100% 瞄准范围）
```
[INFO] Graphics Service: 🎯 目标锁定！距离: 89.2px, 偏移: (32.1, -25.8), 大小: 28x52
[INFO] Graphics Service: 🎮 瞄准状态: 粗略瞄准 | 建议移动: X+32.1, Y-25.8
```

### ❌ 超出范围
```
[INFO] Graphics Service: 👁️ 发现目标但超出范围: 距离 156.7px > 100px
```

## 📊 检测统计信息

每100帧显示一次统计：

```
[INFO] Graphics Service: 📹 视频处理: 200 帧
[INFO] Graphics Service: 📊 检测统计: 检测率 22.5% | 平均目标数 1.8 | 上次检测 1.3秒前
```

**统计说明：**
- **检测率**: 有目标的帧数占总帧数的百分比
- **平均目标数**: 每次检测到的平均目标数量
- **上次检测**: 距离最后一次检测到目标的时间

## 🎯 不同目标类型的显示

### 检测所有目标（target_class = "ALL"）
```
[INFO] Graphics Service: 🎯 检测到 3 个目标，最近距离 42.1 像素
```

### 只检测人物（target_class = "0"）
```
[INFO] Graphics Service: 🎯 检测到 2 个人物，最近距离 38.5 像素
```

### 只检测车辆（target_class = "1"）
```
[INFO] Graphics Service: 🎯 检测到 1 个车辆，最近距离 67.8 像素
```

## 🔄 实时状态更新

控制台会显示：

```
============================================================
🎯 HohoAI 调试模式已就绪
============================================================
📡 后台服务: 运行中
📝 配置文件: 已打开
🔄 实时更新: 启用
📹 视频捕获: 自动启动
🤖 YOLO检测: 自动启用
🎯 瞄准功能: 已激活

📊 状态更新 #1 - 14:05:30
   📡 服务: 运行中 | 📹 视频: 捕获中 | 🤖 YOLO: 检测中

[INFO] Graphics Service: YOLO model initialized and warmed up successfully
[INFO] Graphics Service: Video capture started successfully
[INFO] Graphics Service: Auto-enabled YOLO detection based on configuration
[INFO] Graphics Service: 📹 视频处理: 100 帧
[INFO] Graphics Service: 🎯 检测到 1 个人物，最近距离 52.3 像素
[INFO] Graphics Service: 🎯 目标锁定！距离: 52.3px, 偏移: (15.2, -8.7), 大小: 32x58
[INFO] Graphics Service: 🎮 瞄准状态: 中等精度 | 建议移动: X+15.2, Y-8.7
[INFO] Graphics Service: 📊 检测统计: 检测率 18.0% | 平均目标数 1.2 | 上次检测 0.5秒前

📊 状态更新 #2 - 14:05:40
   📡 服务: 运行中 | 📹 视频: 捕获中 | 🤖 YOLO: 检测中
```

## ⚙️ 调整检测参数

您可以在配置文件中调整：

```json
{
  "aimbot_settings": {
    "yolo_confidence": 0.6,    // 提高置信度，减少误检
    "target_class": "0",       // 只检测人物
    "aim_range": 120          // 扩大瞄准范围
  }
}
```

保存后立即生效，日志会显示：
```
[INFO] Graphics Service: Configuration file hohoai_config.json changed, reloading...
[INFO] Graphics Service: Configuration changes applied successfully
```

## 🎯 总结

- ✅ **实时检测日志** - 每次检测都会显示详细信息
- ✅ **目标分类显示** - 区分人物、车辆等不同目标
- ✅ **距离和偏移** - 显示精确的位置信息
- ✅ **瞄准状态** - 根据距离显示瞄准精度
- ✅ **统计信息** - 定期显示检测效率统计
- ✅ **实时更新** - 每10秒显示系统状态

现在您可以清楚地看到系统是否检测到目标，以及所有相关的详细信息！🎮🎯
