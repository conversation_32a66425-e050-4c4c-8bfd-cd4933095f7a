#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HohoAI 参数快速调整工具
用于无头模式下快速调整参数
"""

import json
import os
import sys

def load_config():
    """加载配置文件"""
    config_file = "hohoai_config.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置失败: {e}")
            return None
    else:
        print("❌ 配置文件不存在")
        return None

def save_config(config):
    """保存配置文件"""
    config_file = "hohoai_config.json"
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✅ 配置已保存")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def show_current_params(config):
    """显示当前参数"""
    print("\n" + "="*50)
    print("🎯 当前参数设置")
    print("="*50)
    
    aimbot = config["aimbot_settings"]
    print(f"📌 自瞄状态: {'✅ 启用' if aimbot['enabled'] else '❌ 禁用'}")
    print(f"📌 X轴速度: {aimbot['aim_speed_x']}")
    print(f"📌 Y轴速度: {aimbot['aim_speed_y']}")
    print(f"📌 瞄准范围: {aimbot['aim_range']}")
    print(f"📌 X轴偏移: {aimbot['offset_centerx']}")
    print(f"📌 Y轴偏移: {aimbot['offset_centery']}")
    print(f"📌 YOLO置信度: {aimbot['yolo_confidence']}")
    print(f"📌 目标类别: {aimbot['target_class']}")
    
    advanced = config["advanced_settings"]
    print(f"⚙️ 动态瞄准: {'✅ 启用' if advanced['dynamic_aim_range_enabled'] else '❌ 禁用'}")
    print(f"⚙️ 平滑瞄准: {'✅ 启用' if advanced['smooth_aiming'] else '❌ 禁用'}")
    print(f"⚙️ 压枪辅助: {'✅ 启用' if advanced['recoil_suppression_switch'] else '❌ 禁用'}")
    print(f"⚙️ 压枪强度: {advanced['recoil_strength']}")

def quick_adjust_menu(config):
    """快速调整菜单"""
    while True:
        print("\n" + "="*50)
        print("🎮 快速参数调整")
        print("="*50)
        print("1. 调整瞄准速度")
        print("2. 调整瞄准范围")
        print("3. 调整偏移量")
        print("4. 调整YOLO设置")
        print("5. 切换功能开关")
        print("6. 显示当前参数")
        print("7. 保存并退出")
        print("0. 退出不保存")
        
        try:
            choice = input("\n请选择 (0-7): ").strip()
            
            if choice == "1":
                adjust_aim_speed(config)
            elif choice == "2":
                adjust_aim_range(config)
            elif choice == "3":
                adjust_offset(config)
            elif choice == "4":
                adjust_yolo_settings(config)
            elif choice == "5":
                toggle_features(config)
            elif choice == "6":
                show_current_params(config)
            elif choice == "7":
                if save_config(config):
                    print("✅ 参数已保存，无头服务将自动应用新设置")
                break
            elif choice == "0":
                print("👋 退出不保存")
                break
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print("\n👋 退出")
            break

def adjust_aim_speed(config):
    """调整瞄准速度"""
    print("\n🎯 调整瞄准速度")
    aimbot = config["aimbot_settings"]
    
    print(f"当前X轴速度: {aimbot['aim_speed_x']}")
    try:
        new_x = float(input("新的X轴速度 (0.1-2.0): "))
        if 0.1 <= new_x <= 2.0:
            aimbot['aim_speed_x'] = new_x
            print(f"✅ X轴速度已设置为: {new_x}")
        else:
            print("❌ 值超出范围")
    except ValueError:
        print("❌ 无效输入")
    
    print(f"当前Y轴速度: {aimbot['aim_speed_y']}")
    try:
        new_y = float(input("新的Y轴速度 (0.0-2.0): "))
        if 0.0 <= new_y <= 2.0:
            aimbot['aim_speed_y'] = new_y
            print(f"✅ Y轴速度已设置为: {new_y}")
        else:
            print("❌ 值超出范围")
    except ValueError:
        print("❌ 无效输入")

def adjust_aim_range(config):
    """调整瞄准范围"""
    print("\n🎯 调整瞄准范围")
    aimbot = config["aimbot_settings"]
    
    print(f"当前瞄准范围: {aimbot['aim_range']}")
    try:
        new_range = int(input("新的瞄准范围 (50-300): "))
        if 50 <= new_range <= 300:
            aimbot['aim_range'] = new_range
            print(f"✅ 瞄准范围已设置为: {new_range}")
        else:
            print("❌ 值超出范围")
    except ValueError:
        print("❌ 无效输入")

def adjust_offset(config):
    """调整偏移量"""
    print("\n🎯 调整瞄准偏移")
    aimbot = config["aimbot_settings"]
    
    print(f"当前X轴偏移: {aimbot['offset_centerx']}")
    try:
        new_x = float(input("新的X轴偏移 (-1.0 到 1.0): "))
        if -1.0 <= new_x <= 1.0:
            aimbot['offset_centerx'] = new_x
            print(f"✅ X轴偏移已设置为: {new_x}")
        else:
            print("❌ 值超出范围")
    except ValueError:
        print("❌ 无效输入")
    
    print(f"当前Y轴偏移: {aimbot['offset_centery']}")
    try:
        new_y = float(input("新的Y轴偏移 (-1.0 到 1.0): "))
        if -1.0 <= new_y <= 1.0:
            aimbot['offset_centery'] = new_y
            print(f"✅ Y轴偏移已设置为: {new_y}")
        else:
            print("❌ 值超出范围")
    except ValueError:
        print("❌ 无效输入")

def adjust_yolo_settings(config):
    """调整YOLO设置"""
    print("\n🤖 调整YOLO设置")
    aimbot = config["aimbot_settings"]
    
    print(f"当前置信度: {aimbot['yolo_confidence']}")
    try:
        new_conf = float(input("新的置信度 (0.1-0.9): "))
        if 0.1 <= new_conf <= 0.9:
            aimbot['yolo_confidence'] = new_conf
            print(f"✅ 置信度已设置为: {new_conf}")
        else:
            print("❌ 值超出范围")
    except ValueError:
        print("❌ 无效输入")
    
    print(f"当前目标类别: {aimbot['target_class']}")
    print("可选: ALL, 0(人物), 1(车辆)")
    new_class = input("新的目标类别: ").strip()
    if new_class in ["ALL", "0", "1"]:
        aimbot['target_class'] = new_class
        print(f"✅ 目标类别已设置为: {new_class}")
    else:
        print("❌ 无效类别")

def toggle_features(config):
    """切换功能开关"""
    print("\n⚙️ 功能开关")
    aimbot = config["aimbot_settings"]
    advanced = config["advanced_settings"]
    
    features = [
        ("自瞄功能", "aimbot_settings", "enabled"),
        ("动态瞄准范围", "advanced_settings", "dynamic_aim_range_enabled"),
        ("平滑瞄准", "advanced_settings", "smooth_aiming"),
        ("压枪辅助", "advanced_settings", "recoil_suppression_switch"),
        ("自动机器人", "advanced_settings", "auto_robot_enabled")
    ]
    
    for i, (name, section, key) in enumerate(features, 1):
        current = config[section][key]
        status = "✅ 启用" if current else "❌ 禁用"
        print(f"{i}. {name}: {status}")
    
    try:
        choice = int(input("\n选择要切换的功能 (1-5): "))
        if 1 <= choice <= len(features):
            name, section, key = features[choice-1]
            config[section][key] = not config[section][key]
            new_status = "✅ 启用" if config[section][key] else "❌ 禁用"
            print(f"✅ {name} 已切换为: {new_status}")
        else:
            print("❌ 无效选择")
    except ValueError:
        print("❌ 无效输入")

def main():
    """主函数"""
    print("🎮 HohoAI 参数调整工具")
    print("适用于无头模式下的参数调整")
    
    config = load_config()
    if config is None:
        return
    
    print("✅ 配置文件加载成功")
    show_current_params(config)
    
    quick_adjust_menu(config)

if __name__ == "__main__":
    main()
