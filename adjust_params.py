#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HohoAI 参数调整工具 - 简化版
卡密验证成功后自动打开配置文件
"""

import json
import os
import sys
import requests

def load_config():
    """加载配置文件"""
    config_file = "hohoai_config.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置失败: {e}")
            return None
    else:
        print("❌ 配置文件不存在")
        return None

def save_config(config):
    """保存配置文件"""
    config_file = "hohoai_config.json"
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✅ 配置已保存")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def show_current_params(config):
    """显示当前参数"""
    print("\n" + "="*50)
    print("🎯 当前参数设置")
    print("="*50)
    
    aimbot = config["aimbot_settings"]
    print(f"📌 自瞄状态: {'✅ 启用' if aimbot['enabled'] else '❌ 禁用'}")
    print(f"📌 X轴速度: {aimbot['aim_speed_x']}")
    print(f"📌 Y轴速度: {aimbot['aim_speed_y']}")
    print(f"📌 瞄准范围: {aimbot['aim_range']}")
    print(f"📌 X轴偏移: {aimbot['offset_centerx']}")
    print(f"📌 Y轴偏移: {aimbot['offset_centery']}")
    print(f"📌 YOLO置信度: {aimbot['yolo_confidence']}")
    print(f"📌 目标类别: {aimbot['target_class']}")
    
    advanced = config["advanced_settings"]
    print(f"⚙️ 动态瞄准: {'✅ 启用' if advanced['dynamic_aim_range_enabled'] else '❌ 禁用'}")
    print(f"⚙️ 平滑瞄准: {'✅ 启用' if advanced['smooth_aiming'] else '❌ 禁用'}")
    print(f"⚙️ 压枪辅助: {'✅ 启用' if advanced['recoil_suppression_switch'] else '❌ 禁用'}")
    print(f"⚙️ 压枪强度: {advanced['recoil_strength']}")

def auto_open_config():
    """自动打开配置文件"""
    config_file = "hohoai_config.json"

    if os.path.exists(config_file):
        try:
            # 尝试用默认程序打开JSON文件
            if os.name == 'nt':  # Windows
                os.startfile(config_file)
            elif os.name == 'posix':  # macOS/Linux
                os.system(f'open "{config_file}"' if sys.platform == 'darwin' else f'xdg-open "{config_file}"')

            print(f"✅ 已自动打开配置文件: {config_file}")
            print("💡 编辑完成后保存文件，无头服务将自动应用新设置")
            return True

        except Exception as e:
            print(f"❌ 无法自动打开配置文件: {e}")
            print(f"💡 请手动打开文件: {config_file}")
            return False
    else:
        print("❌ 配置文件不存在")
        return False

# 删除了复杂的菜单函数，改为直接打开配置文件编辑

def verify_card_key():
    """卡密验证"""
    import requests

    # 卡密验证API
    CARD_API_URL = "http://************:5000/api/check_card"
    API_TOKEN = "3f8c1a9e4b9d63a01e6d2c14c956aa7b"

    for attempt in range(3):
        try:
            card_key = input(f"请输入卡密 (第{attempt+1}/3次): ").strip()

            if not card_key:
                print("❌ 卡密不能为空")
                continue

            # 发送验证请求
            response = requests.post(
                CARD_API_URL,
                json={"card_key": card_key},
                headers={"X-API-Token": API_TOKEN},
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("valid"):
                    print("✅ 卡密验证通过！")
                    return True
                else:
                    print(f"❌ 卡密验证失败: {data.get('msg', '未知错误')}")
            else:
                print(f"❌ 服务器错误: {response.status_code}")

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
        except Exception as e:
            print(f"❌ 验证错误: {e}")

    print("❌ 卡密验证失败，程序退出")
    return False

def main():
    """主函数"""
    print("🎮 HohoAI 参数调整工具")
    print("Graphics Driver Service Configuration Tool")

    # 卡密验证
    if not verify_card_key():
        return

    print("\n🔧 正在加载配置...")
    config = load_config()
    if config is None:
        return

    print("✅ 配置文件加载成功")
    print("🚀 自动打开配置文件进行编辑...")

    # 自动打开配置文件
    if auto_open_config():
        print("\n💡 使用说明:")
        print("   - 编辑完成后保存文件")
        print("   - 无头服务将自动检测并应用新设置")
        print("   - 按任意键退出...")
        input()
    else:
        print("\n📋 当前配置:")
        show_current_params(config)

if __name__ == "__main__":
    main()
