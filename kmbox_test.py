#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KMBox 测试和配置工具
用于测试KMBox连接和配置参数
"""

import json
import os
import sys
import time

def load_config():
    """加载配置文件"""
    config_file = "hohoai_config.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置失败: {e}")
            return None
    else:
        print("❌ 配置文件不存在")
        return None

def save_config(config):
    """保存配置文件"""
    config_file = "hohoai_config.json"
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✅ 配置已保存")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def test_kmbox_connection(ip, port, uuid):
    """测试KMBox连接"""
    try:
        print(f"🔧 正在测试KMBox连接...")
        print(f"   IP: {ip}")
        print(f"   端口: {port}")
        print(f"   UUID: {uuid}")
        
        # 尝试导入KMBox模块
        try:
            from Module.control import kmNet
            print("✅ KMBox模块加载成功")
        except ImportError:
            print("❌ KMBox模块未找到，请确保Module/control.py存在")
            return False
        
        # 初始化连接
        print("🔌 正在连接KMBox...")
        result = kmNet.init(ip, port, uuid)
        
        if result == 0:
            print("✅ KMBox连接成功！")
            
            # 测试获取鼠标位置
            try:
                x, y = kmNet.getxy()
                print(f"📍 当前鼠标位置: ({x}, {y})")
            except Exception as e:
                print(f"⚠️ 获取鼠标位置失败: {e}")
            
            # 测试鼠标移动
            print("🖱️ 执行移动测试...")
            test_moves = [
                (10, 0),   # 右移10像素
                (0, 10),   # 下移10像素
                (-10, 0),  # 左移10像素
                (0, -10)   # 上移10像素
            ]
            
            for i, (dx, dy) in enumerate(test_moves, 1):
                try:
                    kmNet.move(dx, dy)
                    print(f"   测试 {i}/4: 移动 ({dx:+d}, {dy:+d}) ✅")
                    time.sleep(0.5)
                except Exception as e:
                    print(f"   测试 {i}/4: 移动失败 - {e}")
            
            print("✅ KMBox移动测试完成")
            return True
            
        else:
            print(f"❌ KMBox连接失败，错误代码: {result}")
            print("   可能的原因:")
            print("   - IP地址不正确")
            print("   - 端口被占用")
            print("   - UUID不匹配")
            print("   - KMBox设备未连接")
            return False
            
    except Exception as e:
        print(f"❌ KMBox测试异常: {e}")
        return False

def configure_kmbox():
    """配置KMBox参数"""
    print("\n" + "="*50)
    print("🎮 KMBox 配置")
    print("="*50)
    
    config = load_config()
    if not config:
        return
    
    kmbox_settings = config.get("kmbox_settings", {})
    
    print(f"当前配置:")
    print(f"  启用状态: {'✅ 启用' if kmbox_settings.get('enabled', False) else '❌ 禁用'}")
    print(f"  IP地址: {kmbox_settings.get('ip', '*************')}")
    print(f"  端口: {kmbox_settings.get('port', 8888)}")
    print(f"  UUID: {kmbox_settings.get('uuid', '6EC5D1A1')}")
    
    print("\n请输入新的配置（直接回车保持当前值）:")
    
    # IP地址
    current_ip = kmbox_settings.get('ip', '*************')
    new_ip = input(f"IP地址 [{current_ip}]: ").strip()
    if new_ip:
        kmbox_settings['ip'] = new_ip
    else:
        kmbox_settings['ip'] = current_ip
    
    # 端口
    current_port = kmbox_settings.get('port', 8888)
    new_port = input(f"端口 [{current_port}]: ").strip()
    if new_port:
        try:
            kmbox_settings['port'] = int(new_port)
        except ValueError:
            print("❌ 端口必须是数字")
            return
    else:
        kmbox_settings['port'] = current_port
    
    # UUID
    current_uuid = kmbox_settings.get('uuid', '6EC5D1A1')
    new_uuid = input(f"UUID [{current_uuid}]: ").strip()
    if new_uuid:
        kmbox_settings['uuid'] = new_uuid
    else:
        kmbox_settings['uuid'] = current_uuid
    
    # 是否启用
    current_enabled = kmbox_settings.get('enabled', False)
    enable_choice = input(f"启用KMBox? (y/n) [{'y' if current_enabled else 'n'}]: ").strip().lower()
    if enable_choice == 'y':
        kmbox_settings['enabled'] = True
    elif enable_choice == 'n':
        kmbox_settings['enabled'] = False
    else:
        kmbox_settings['enabled'] = current_enabled
    
    # 保存配置
    config['kmbox_settings'] = kmbox_settings
    if save_config(config):
        print("✅ KMBox配置已保存")
        
        # 测试新配置
        if kmbox_settings['enabled']:
            test_choice = input("是否测试新配置? (y/n): ").strip().lower()
            if test_choice == 'y':
                test_kmbox_connection(
                    kmbox_settings['ip'],
                    kmbox_settings['port'],
                    kmbox_settings['uuid']
                )

def main():
    """主函数"""
    print("🎮 KMBox 测试和配置工具")
    print("Graphics Driver Service - KMBox Manager")
    
    while True:
        print("\n" + "="*50)
        print("选择操作:")
        print("1. 测试KMBox连接")
        print("2. 配置KMBox参数")
        print("3. 查看当前配置")
        print("4. 启用/禁用KMBox")
        print("0. 退出")
        
        try:
            choice = input("\n请选择 (0-4): ").strip()
            
            if choice == "0":
                print("👋 退出")
                break
            elif choice == "1":
                config = load_config()
                if config:
                    kmbox_settings = config.get("kmbox_settings", {})
                    test_kmbox_connection(
                        kmbox_settings.get('ip', '*************'),
                        kmbox_settings.get('port', 8888),
                        kmbox_settings.get('uuid', '6EC5D1A1')
                    )
            elif choice == "2":
                configure_kmbox()
            elif choice == "3":
                config = load_config()
                if config:
                    kmbox_settings = config.get("kmbox_settings", {})
                    print("\n📋 当前KMBox配置:")
                    print(f"  启用状态: {'✅ 启用' if kmbox_settings.get('enabled', False) else '❌ 禁用'}")
                    print(f"  IP地址: {kmbox_settings.get('ip', '*************')}")
                    print(f"  端口: {kmbox_settings.get('port', 8888)}")
                    print(f"  UUID: {kmbox_settings.get('uuid', '6EC5D1A1')}")
            elif choice == "4":
                config = load_config()
                if config:
                    kmbox_settings = config.get("kmbox_settings", {})
                    current_enabled = kmbox_settings.get('enabled', False)
                    new_enabled = not current_enabled
                    kmbox_settings['enabled'] = new_enabled
                    config['kmbox_settings'] = kmbox_settings
                    
                    if save_config(config):
                        status = "启用" if new_enabled else "禁用"
                        print(f"✅ KMBox已{status}")
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print("\n👋 退出")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
