#!/usr/bin/env python3
"""
将 YOLOv8s_apex_teammate_enemy.pt 转换为 GPU 优化的 ONNX 模型
"""

import os
import torch
import numpy as np
from ultralytics import YOL<PERSON>

def convert_apex_model_to_gpu_onnx():
    """转换 Apex 模型为 GPU 优化 ONNX"""
    
    # 模型文件路径
    pt_path = "MY_apex/YOLOv8s_apex_teammate_enemy.pt"
    onnx_path = "MY_apex/YOLOv8s_apex_teammate_enemy_gpu.onnx"
    
    print("🎯 Apex YOLO 模型 GPU ONNX 转换工具")
    print("=" * 50)
    print(f"📁 输入文件: {pt_path}")
    print(f"📁 输出文件: {onnx_path}")
    
    # 检查输入文件
    if not os.path.exists(pt_path):
        print(f"❌ 模型文件不存在: {pt_path}")
        return False
    
    try:
        # 检查 GPU 可用性
        if torch.cuda.is_available():
            print(f"🎮 GPU 可用: {torch.cuda.get_device_name(0)}")
            print(f"🔧 CUDA 版本: {torch.version.cuda}")
            device = "cuda"
        else:
            print("💻 GPU 不可用，使用 CPU 进行转换")
            device = "cpu"
        
        # 清理 GPU 缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        print("\n📦 加载 YOLO 模型...")
        model = YOLO(pt_path)
        
        # 移动模型到指定设备
        model.to(device)
        
        print("🔄 开始转换为 GPU 优化 ONNX...")
        
        # GPU 优化的转换参数
        export_params = {
            'format': 'onnx',
            'imgsz': 640,
            'half': False,  # 先使用 FP32 确保兼容性
            'dynamic': False,
            'simplify': True,
            'opset': 11,  # 使用兼容性好的 opset 版本
            'device': device,
        }
        
        print("⚡ 转换参数:")
        for key, value in export_params.items():
            print(f"   {key}: {value}")
        
        # 执行转换
        success = model.export(**export_params)
        
        # 检查转换结果
        expected_output = pt_path.replace('.pt', '.onnx')
        if os.path.exists(expected_output):
            # 移动到目标位置
            if expected_output != onnx_path:
                import shutil
                shutil.move(expected_output, onnx_path)
                print(f"📁 文件已移动到: {onnx_path}")
            
            # 显示文件信息
            file_size = os.path.getsize(onnx_path) / (1024 * 1024)
            print(f"✅ 转换成功!")
            print(f"📊 输出文件大小: {file_size:.2f} MB")
            
            return True
        else:
            print("❌ 转换失败，未找到输出文件")
            return False
            
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        return False

def test_gpu_onnx_model():
    """测试转换后的 GPU ONNX 模型"""
    
    onnx_path = "MY_apex/YOLOv8s_apex_teammate_enemy_gpu.onnx"
    
    print(f"\n🧪 测试 GPU ONNX 模型...")
    print(f"📁 模型文件: {onnx_path}")
    
    if not os.path.exists(onnx_path):
        print("❌ ONNX 文件不存在")
        return False
    
    try:
        import onnxruntime as ort
        
        print("🔍 检查可用的执行提供程序...")
        available_providers = ort.get_available_providers()
        print("可用提供程序:")
        for provider in available_providers:
            if 'CUDA' in provider:
                print(f"   ✅ {provider} (GPU)")
            else:
                print(f"   📋 {provider}")
        
        # 创建推理会话（GPU 优先）
        providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
        session = ort.InferenceSession(onnx_path, providers=providers)
        
        used_provider = session.get_providers()[0]
        print(f"🚀 使用的执行提供程序: {used_provider}")
        
        # 获取模型信息
        input_info = session.get_inputs()[0]
        output_info = session.get_outputs()[0]
        
        print(f"📊 输入信息:")
        print(f"   名称: {input_info.name}")
        print(f"   形状: {input_info.shape}")
        print(f"   类型: {input_info.type}")
        
        print(f"📊 输出信息:")
        print(f"   名称: {output_info.name}")
        print(f"   形状: {output_info.shape}")
        print(f"   类型: {output_info.type}")
        
        # 创建测试输入
        input_shape = input_info.shape
        test_input = np.random.randn(*input_shape).astype(np.float32)
        
        print("🔄 执行测试推理...")
        outputs = session.run(None, {input_info.name: test_input})
        
        print(f"✅ 推理成功!")
        print(f"📊 输出形状: {[out.shape for out in outputs]}")
        
        # 性能测试
        print("\n⏱️  性能测试 (10次推理)...")
        import time
        
        start_time = time.time()
        for i in range(10):
            outputs = session.run(None, {input_info.name: test_input})
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 10 * 1000  # 转换为毫秒
        fps = 1000 / avg_time
        
        print(f"📊 平均推理时间: {avg_time:.2f} ms")
        print(f"📊 理论 FPS: {fps:.1f}")
        
        if 'CUDA' in used_provider:
            print("🎮 GPU 加速正常工作!")
        else:
            print("💻 使用 CPU 推理")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎯 YOLOv8s Apex 模型 GPU ONNX 转换")
    print("=" * 60)
    
    # 转换模型
    if convert_apex_model_to_gpu_onnx():
        print("\n🎉 模型转换成功!")
        
        # 测试模型
        if test_gpu_onnx_model():
            print("\n✅ 测试通过! GPU ONNX 模型可以正常使用")
            print("\n💡 使用建议:")
            print("1. 在您的代码中使用 MY_apex/YOLOv8s_apex_teammate_enemy_gpu.onnx")
            print("2. 确保使用 ONNX Runtime GPU 版本")
            print("3. 设置执行提供程序为 ['CUDAExecutionProvider', 'CPUExecutionProvider']")
        else:
            print("\n⚠️  转换成功但测试失败，模型文件仍可使用")
    else:
        print("\n❌ 模型转换失败")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎉 转换完成!")
        print(f"📁 GPU 优化 ONNX 文件: MY_apex/YOLOv8s_apex_teammate_enemy_gpu.onnx")
    else:
        print(f"\n❌ 转换失败，请检查错误信息")
