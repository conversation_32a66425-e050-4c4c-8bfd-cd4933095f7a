#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NVIDIA Display Container Service
Graphics Driver Background Process
"""

import os
import sys
import ctypes
import subprocess
import time

def set_process_name():
    """设置进程名称和描述"""
    try:
        # 设置控制台标题
        ctypes.windll.kernel32.SetConsoleTitleW("NVIDIA Display Container LS")
        
        # 尝试修改进程名称（需要额外的库）
        try:
            import setproctitle
            setproctitle.setproctitle("nvdisplay")
        except ImportError:
            pass
            
    except Exception:
        pass

def create_service_environment():
    """创建服务环境"""
    # 创建NVIDIA相关的临时文件
    nvidia_files = {
        "nvdisplay.log": f"NVIDIA Display Container LS started at {time.strftime('%Y-%m-%d %H:%M:%S')}\n",
        "gpu_status.tmp": "GPU Status: Active\nDriver Version: 531.79\n",
        "display_config.dat": "Display Configuration Loaded\nMulti-Monitor: Enabled\n"
    }
    
    for filename, content in nvidia_files.items():
        try:
            with open(filename, 'w') as f:
                f.write(content)
        except:
            pass

def main():
    """主函数"""
    # 设置进程伪装
    set_process_name()
    
    # 创建服务环境
    create_service_environment()
    
    print("NVIDIA Display Container LS")
    print("Copyright (c) NVIDIA Corporation. All rights reserved.")
    print("Starting display services...")
    
    # 启动实际的服务
    try:
        # 导入并启动无头服务
        from headless_service import start_headless_service
        start_headless_service()
    except ImportError:
        # 如果导入失败，直接运行
        subprocess.run([sys.executable, "headless_service.py"])

if __name__ == "__main__":
    main()
