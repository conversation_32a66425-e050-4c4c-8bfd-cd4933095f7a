@echo off
echo 安装ONNX相关包...
echo.

echo 步骤1: 清理现有ONNX Runtime安装
pip uninstall onnxruntime onnxruntime-gpu -y

echo.
echo 步骤2: 安装核心包
pip install onnx==1.17.0

echo.
echo 步骤3: 选择ONNX Runtime版本
echo 请选择要安装的版本:
echo 1. GPU版本 (需要CUDA环境)
echo 2. CPU版本 (推荐，稳定)
echo.
set /p choice="请输入选择 (1 或 2): "

if "%choice%"=="1" (
    echo 安装GPU版本...
    pip install onnxruntime-gpu==1.13.1
    echo GPU版本安装完成！
    echo 注意：需要CUDA 11.8 + cuDNN 8.x
) else if "%choice%"=="2" (
    echo 安装CPU版本...
    pip install onnxruntime==1.22.1
    echo CPU版本安装完成！
) else (
    echo 无效选择，默认安装CPU版本...
    pip install onnxruntime==1.22.1
)

echo.
echo 步骤4: 安装辅助工具
pip install onnxslim==0.1.61

echo.
echo 安装完成！验证安装...
python -c "import onnxruntime; print('ONNX Runtime版本:', onnxruntime.__version__)"
python -c "import onnx; print('ONNX版本:', onnx.__version__)"

echo.
echo 安装总结:
pip list | findstr onnx

pause
