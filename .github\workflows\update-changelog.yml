name: Generate Changelog

on:
  push:
    branches:
      - dev
permissions:

  contents: write

env:
  TZ: Asia/Shanghai

jobs:
  generate-changelog:
    runs-on: ubuntu-latest

    steps:

      - name: Show the timezone
        run: |
          echo "The timezone is $TZ"
          date

      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: Append to CHANGELOG.md
        run: |
          CURRENT_DATE=$(date +%Y-%m-%d)

          NEW_ENTRIES="- $(git log -1 --pretty=format:'[`%h`](%H) %s')"

          # Check if the current date already exists in CHANGELOG.md
          if grep -q "## \[$CURRENT_DATE\] - New Updates" CHANGELOG.md; then
            # If an entry for the same date exists, append the new commit info
            HEAD_CONTENT=$(head -n 2 CHANGELOG.md)
            TAIL_CONTENT=$(tail -n +3 CHANGELOG.md)
            NEW_SECTION="$HEAD_CONTENT\n$NEW_ENTRIES\n$TAIL_CONTENT"
            echo -e "$NEW_SECTION" > CHANGELOG.md
            echo "Appended new commit info to the existing date entry"
          else
            # If no entry for the current date exists, create a new entry
            HEAD_CONTENT=$(head -n 1 CHANGELOG.md)
            TAIL_CONTENT=$(tail -n +2 CHANGELOG.md)
            NEW_SECTION="## [$CURRENT_DATE] - New Updates\n$NEW_ENTRIES\n"
            echo -e "$HEAD_CONTENT\n$NEW_SECTION\n$TAIL_CONTENT" > CHANGELOG.md
            echo "Created a new date entry and added commit info"
          fi

      - name: Display CHANGELOG.md
        run: |
          cat CHANGELOG.md

      - name: Commit and push changes
        run: |
          git add CHANGELOG.md
          git commit -m "Update CHANGELOG.md with new entries" || echo "No changes to commit"

          git pull --allow-unrelated-histories
          git push
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
