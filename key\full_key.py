import os
import json
import time
import hashlib
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from flask import Flask, request, jsonify, render_template, redirect, url_for, session, flash
from datetime import datetime, timedelta
from functools import wraps

DB_FILE = os.path.join("key", "card_db.json")

app = Flask(__name__)
app.secret_key = 'abc123!@#超级安全'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(minutes=30)

# 管理员凭据
ADMIN_USER = os.getenv('ADMIN_USER')
ADMIN_PASS = os.getenv('ADMIN_PASS')
if not ADMIN_USER or not ADMIN_PASS:
    raise RuntimeError("请先设置环境变量 ADMIN_USER / ADMIN_PASS 再启动服务器")

ADMIN_CREDENTIALS = {
    'username': ADMIN_USER,
    'password': ADMIN_PASS
}

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        if username == ADMIN_CREDENTIALS['username'] and password == ADMIN_CREDENTIALS['password']:
            session['logged_in'] = True
            return redirect(url_for('index'))
        flash('用户名或密码错误', 'error')
    return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>卡密管理登录</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { padding: 20px; background-color: #f8f9fa; }
                .login-form { max-width: 400px; margin: 50px auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="login-form">
                    <h2 class="mb-4 text-center">卡密管理系统</h2>
                    <form method="post">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" name="username" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <input type="password" name="password" class="form-control" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">登录</button>
                    </form>
                </div>
            </div>
        </body>
        </html>
    '''

@app.route('/logout')
@login_required
def logout():
    session.pop('logged_in', None)
    return redirect(url_for('login'))

def load_db():
    db_dir = os.path.dirname(DB_FILE)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)

    if not os.path.exists(DB_FILE):
        with open(DB_FILE, "w", encoding="utf-8") as f:
            json.dump({}, f, ensure_ascii=False)
    with open(DB_FILE, "r") as f:
        return json.load(f)

def save_db(db):
    with open(DB_FILE, "w") as f:
        json.dump(db, f, indent=2)

def hash_card(card, salt):
    return hashlib.sha256((salt + card).encode('utf-8')).hexdigest()

def generate_salt():
    return hashlib.sha256(os.urandom(60)).hexdigest()

_AES_KEY = b'cascade123456789'  # 16-byte key

# ---------------- AES 工具函式 -----------------
def _encrypt_plain(text: str) -> str:
    """把明文字串加密成 enc:IV:ct 形式；若已帶 enc: 前綴則直接回傳。"""
    if isinstance(text, str) and text.startswith('enc:'):
        return text
    iv = os.urandom(16)
    cipher = AES.new(_AES_KEY, AES.MODE_CBC, iv)
    ct_bytes = cipher.encrypt(pad(text.encode('utf-8'), AES.block_size))
    return 'enc:' + base64.b64encode(iv).decode() + ':' + base64.b64encode(ct_bytes).decode()


def _decrypt_plain(enc: str) -> str:
    """把 enc:IV:ct 解密回明文；若格式錯誤回傳錯字串"""
    if not enc or not enc.startswith('enc:'):
        return enc  # 非加密格式直接回傳
    try:
        iv_b64, ct_b64 = enc[4:].split(':', 1)
        iv = base64.b64decode(iv_b64)
        ct = base64.b64decode(ct_b64)
        cipher = AES.new(_AES_KEY, AES.MODE_CBC, iv)
        pt = unpad(cipher.decrypt(ct), AES.block_size).decode('utf-8')
        return pt
    except Exception as e:
        return f"解密失败:{e}"

# ---------------- Jinja2 过滤器 ----------------

@app.template_filter('datetimeformat')
def datetimeformat(value):
    try:
        if not value or value <= 0:
            return "-"
        return datetime.fromtimestamp(value).strftime('%Y-%m-%d %H:%M')
    except Exception:
        return "-"

@app.route('/')
@login_required
def index():
    db = load_db()
    now = time.time()
    cards_dict = {}

    for key, data in list(db.items()):
        # 自動刪除過期卡密
        if now > data['expire_at']:
            del db[key]
            continue
        card = data.copy()
        card['key'] = key
        time_left = data['expire_at'] - now
        card['left_minutes'] = max(0, int(round(time_left / 60)))
        card['expired'] = time_left <= 0
        cards_dict[key] = card

    if cards_dict != db:
        save_db(db)

    return render_template('index.html', cards=cards_dict)

@app.route('/add', methods=['POST'])
@login_required
def add_card():
    db = load_db()
    card = request.form['key'].strip()
    salt = generate_salt()
    key = hash_card(card, salt)
    minutes = int(request.form['minutes'])
    now = time.time()

    encrypted_plain = _encrypt_plain(card)

    db[key] = {
        "plain": encrypted_plain,
        "salt": salt,
        "valid": True,
        "expire_at": now + minutes * 60,
        "msg": "ok"
    }
    save_db(db)
    return redirect(url_for('index'))

@app.route('/disable/<path:key>')
@login_required
def disable_card(key):
    db = load_db()
    if key in db:
        db[key]['valid'] = False
        db[key]['msg'] = "已被禁用"
        save_db(db)
    return redirect(url_for('index'))

@app.route('/enable/<path:key>')
@login_required
def enable_card(key):
    db = load_db()
    if key in db:
        db[key]['valid'] = True
        db[key]['msg'] = "ok"
        save_db(db)
    return redirect(url_for('index'))

@app.route('/delete/<path:key>')
@login_required
def delete_card(key):
    db = load_db()
    if key in db:
        del db[key]
        save_db(db)
    return redirect(url_for('index'))

@app.route('/api/check_card', methods=['POST'])
def check_card():
    db = load_db()
    card_key = request.json.get("card_key")
    now = time.time()

    for k, v in db.items():
        stored_key = v["plain"]
        if stored_key.startswith('enc:'):
            stored_key = _decrypt_plain(stored_key)
        if stored_key == card_key and v["valid"] and now < v["expire_at"]:
            # 取消自動續期，僅確認有效性
            return jsonify({"valid": True, "msg": "卡密有效"})
    return jsonify({"valid": False, "msg": "卡密无效或已过期"})

@app.route('/decrypt', methods=['POST'])
@login_required
def decrypt_card():
    """解密加密卡密，供前端查看"""
    data = request.get_json()
    encrypted = data.get('encrypted', '')
    if not encrypted.startswith('enc:'):
        encrypted = 'enc:' + encrypted
    plaintext = _decrypt_plain(encrypted)
    if plaintext.startswith('解密失败'):
        return jsonify({"error": plaintext}), 400
    return jsonify({"decrypted": plaintext})

@app.route('/delete_card', methods=['POST'])
@login_required
def delete_card_api():
    data = request.get_json()
    key = data.get('key')
    db = load_db()
    if key in db:
        del db[key]
        save_db(db)
        return jsonify({"success": True})
    return jsonify({"success": False, "message": "Not found"}), 404

# === 新增: 共享密钥保护的远程卡密管理 API ===
API_TOKEN = os.getenv('API_TOKEN')
if not API_TOKEN:
    raise RuntimeError("请先设置环境变量 API_TOKEN 以启用远程管理 API")


def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        # 从 Header 或 JSON 体里获取 token
        token = request.headers.get('X-API-Token')
        if not token and request.is_json:
            token = (request.get_json() or {}).get('token')
        if token != API_TOKEN:
            return jsonify({"success": False, "msg": "无效的 API_TOKEN"}), 401
        return f(*args, **kwargs)
    return decorated


@app.route('/api/card/add', methods=['POST'])
@token_required
def api_add_card():
    """远程新增卡密，参数: {card, minutes}"""
    data = request.get_json() or {}
    card = (data.get('card') or '').strip()
    minutes = int(data.get('minutes', 0))
    if not card or minutes <= 0:
        return jsonify({"success": False, "msg": "缺少 card 或 minutes 参数"}), 400

    db = load_db()
    salt = generate_salt()
    key = hash_card(card, salt)
    now = time.time()
    encrypted_plain = _encrypt_plain(card)

    db[key] = {
        "plain": encrypted_plain,
        "salt": salt,
        "valid": True,
        "expire_at": now + minutes * 60,
        "msg": "ok"
    }
    save_db(db)
    return jsonify({"success": True, "msg": "添加成功", "key": key})


@app.route('/api/card/update_status', methods=['POST'])
@token_required
def api_update_status():
    """启用/禁用/删除卡密，参数: {card, status} status 可为 enable/disable/delete"""
    data = request.get_json() or {}
    card = (data.get('card') or '').strip()
    status = data.get('status')
    if status not in ('enable', 'disable', 'delete'):
        return jsonify({"success": False, "msg": "status 仅能是 enable/disable/delete"}), 400

    db = load_db()
    target_key = None
    for k, v in db.items():
        stored_key = v["plain"]
        if stored_key.startswith('enc:'):
            stored_key = _decrypt_plain(stored_key)
        if stored_key == card:
            target_key = k
            break
    if not target_key:
        return jsonify({"success": False, "msg": "未找到对应卡密"}), 404

    if status == 'delete':
        del db[target_key]
    else:
        db[target_key]['valid'] = (status == 'enable')
        db[target_key]['msg'] = "ok" if status == 'enable' else "已被禁用"
    save_db(db)
    return jsonify({"success": True, "msg": f"{status} 操作成功"})


# === 新增: 取得卡密列表 API ===


@app.route('/api/card/list', methods=['GET'])
@token_required
def api_list_cards():
    """列出所有卡密 (含解密明文)。返回格式: { success, cards: [ {card, hash_key, valid, expire_at} ] }"""
    db = load_db()
    result = []
    now = time.time()
    for k, v in db.items():
        # 只返回未過期且 expire_at > now
        expire_at = v.get("expire_at", 0)
        if not expire_at or expire_at <= now:
            continue  # 跳過已過期或時間為 0

        plain = v.get("plain", "")
        if plain.startswith('enc:'):
            plain = _decrypt_plain(plain)
        result.append({
            "card": plain,
            "hash_key": k,
            "valid": v.get("valid", False),
            "expire_at": expire_at,
        })
    return jsonify({"success": True, "cards": result})

if __name__ == "__main__":
    print("启动完整版卡密管理服务器...")
    print("管理界面: http://localhost:5000")
    print("已从环境变量读取管理员凭据。登陆用户名/密码不再写死在代码里。")
    app.run(host='0.0.0.0', port=5000, debug=True) 