#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HohoAI 服务启动器
提供多种启动选项
"""

import os
import sys
import subprocess
import argparse

def print_banner():
    """显示启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                Graphics Driver Service Launcher              ║
    ║                      v27.20.100.8681                        ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  选择启动模式                                                ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def show_options():
    """显示启动选项"""
    options = """
    启动选项：
    
    1. 🔒 安全模式 (推荐)
       - 需要卡密验证
       - 完全隐蔽运行
       - 最高安全等级
       
    2. 🔧 调试模式
       - 跳过卡密验证
       - 用于测试和调试
       - 仅限开发使用
       
    3. ⚙️ 参数调整
       - 启动参数调整工具
       - 需要卡密验证
       - 自动打开配置文件
    """
    print(options)

def start_secure_mode():
    """启动安全模式（需要卡密）"""
    print("🔒 启动安全模式...")
    try:
        subprocess.run([sys.executable, "headless_service.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def start_debug_mode():
    """启动调试模式（跳过卡密）"""
    print("🔧 启动调试模式...")
    print("⚠️  警告: 调试模式跳过安全验证，仅用于测试！")
    
    # 设置环境变量跳过卡密验证
    os.environ['SKIP_LICENSE_CHECK'] = 'true'
    
    try:
        subprocess.run([sys.executable, "headless_service.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def start_config_tool():
    """启动参数调整工具"""
    print("⚙️ 启动参数调整工具...")
    try:
        subprocess.run([sys.executable, "adjust_params.py"], check=True)
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="HohoAI 服务启动器")
    parser.add_argument("--mode", choices=["secure", "debug", "config"], help="启动模式")
    parser.add_argument("--skip-auth", action="store_true", help="跳过卡密验证（调试用）")
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.skip_auth:
        print("🔧 调试模式: 跳过卡密验证")
        start_debug_mode()
        return
    
    if args.mode:
        if args.mode == "secure":
            start_secure_mode()
        elif args.mode == "debug":
            start_debug_mode()
        elif args.mode == "config":
            start_config_tool()
        return
    
    # 交互式模式选择
    show_options()
    
    while True:
        try:
            choice = input("\n请选择启动模式 (1-3): ").strip()
            
            if choice == "1":
                start_secure_mode()
                break
            elif choice == "2":
                confirm = input("⚠️  确定要使用调试模式吗？(y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    start_debug_mode()
                    break
                else:
                    print("已取消调试模式")
            elif choice == "3":
                start_config_tool()
                break
            else:
                print("❌ 无效选择，请输入 1-3")
                
        except KeyboardInterrupt:
            print("\n👋 退出启动器")
            sys.exit(0)
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
