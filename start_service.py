#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HohoAI 服务启动器
提供多种启动选项
"""

import os
import sys
import subprocess
import argparse
import requests
import time
import threading

def print_banner():
    """显示启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                Graphics Driver Service Launcher              ║
    ║                      v27.20.100.8681                        ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  选择启动模式                                                ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def show_options():
    """显示启动选项"""
    options = """
    启动选项：
    
    1. 🔒 安全模式 (推荐)
       - 需要卡密验证
       - 完全隐蔽运行
       - 最高安全等级
       
    2. 🔧 调试模式
       - 跳过卡密验证
       - 用于测试和调试
       - 仅限开发使用
       
    3. ⚙️ 参数调整
       - 启动参数调整工具
       - 需要卡密验证
       - 自动打开配置文件
    """
    print(options)

def start_secure_mode():
    """启动安全模式（需要卡密）"""
    print("🔒 启动安全模式...")
    try:
        subprocess.run([sys.executable, "headless_service.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def verify_card_key():
    """卡密验证"""
    import requests

    # 卡密验证API
    CARD_API_URL = "http://************:5000/api/check_card"
    API_TOKEN = "3f8c1a9e4b9d63a01e6d2c14c956aa7b"

    for attempt in range(3):
        try:
            card_key = input(f"请输入卡密 (第{attempt+1}/3次): ").strip()

            if not card_key:
                print("❌ 卡密不能为空")
                continue

            print("🔍 正在验证卡密...")

            # 发送验证请求
            response = requests.post(
                CARD_API_URL,
                json={"card_key": card_key},
                headers={"X-API-Token": API_TOKEN},
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("valid"):
                    print("✅ 卡密验证通过！")
                    return True
                else:
                    print(f"❌ 卡密验证失败: {data.get('msg', '未知错误')}")
            else:
                print(f"❌ 服务器错误: {response.status_code}")

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
        except Exception as e:
            print(f"❌ 验证错误: {e}")

    print("❌ 卡密验证失败")
    return False

def auto_open_config():
    """自动打开配置文件"""
    config_file = "hohoai_config.json"

    if os.path.exists(config_file):
        try:
            # 尝试用默认程序打开JSON文件
            if os.name == 'nt':  # Windows
                os.startfile(config_file)
            elif os.name == 'posix':  # macOS/Linux
                os.system(f'open "{config_file}"' if sys.platform == 'darwin' else f'xdg-open "{config_file}"')

            print(f"✅ 已自动打开配置文件: {config_file}")
            print("💡 编辑完成后保存文件，服务将自动应用新设置")
            return True

        except Exception as e:
            print(f"❌ 无法自动打开配置文件: {e}")
            print(f"💡 请手动打开文件: {config_file}")
            return False
    else:
        print("❌ 配置文件不存在")
        return False

def start_debug_mode():
    """启动调试模式（验证卡密后自动打开配置）"""
    print("🔧 启动调试模式...")

    # 卡密验证
    if not verify_card_key():
        print("❌ 卡密验证失败，无法启动服务")
        return

    print("🚀 卡密验证成功，启动服务并打开配置文件...")

    # 设置环境变量跳过服务内部的卡密验证（因为这里已经验证过了）
    os.environ['SKIP_LICENSE_CHECK'] = 'true'

    # 启动无头服务（后台进程）
    try:
        print("📡 正在启动后台服务...")
        service_process = subprocess.Popen([sys.executable, "headless_service.py"])

        # 等待服务启动
        import time
        time.sleep(2)

        print("✅ 后台服务已启动")
        print("📝 正在打开配置文件...")

        # 自动打开配置文件
        if auto_open_config():
            print("\n" + "="*60)
            print("🎯 HohoAI 调试模式已就绪")
            print("="*60)
            print("📡 后台服务: 运行中")
            print("📝 配置文件: 已打开")
            print("🔄 实时更新: 启用")
            print("📹 视频捕获: 自动启动")
            print("🤖 YOLO检测: 自动启用")
            print("🎯 瞄准功能: 已激活")
            print("\n💡 使用说明:")
            print("   - 编辑配置文件中的参数")
            print("   - 保存文件后自动应用到服务")
            print("   - 视频和YOLO会自动运行")
            print("   - 按 Ctrl+C 停止所有服务")
            print("="*60)

            # 显示实时状态
            def show_status():
                status_count = 0
                while True:
                    try:
                        time.sleep(10)  # 每10秒显示一次状态
                        status_count += 1
                        print(f"\n📊 状态更新 #{status_count} - {time.strftime('%H:%M:%S')}")
                        print("   📡 服务: 运行中 | 📹 视频: 捕获中 | 🤖 YOLO: 检测中")
                    except:
                        break

            # 启动状态显示线程
            status_thread = threading.Thread(target=show_status, daemon=True)
            status_thread.start()

            # 等待用户停止
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 正在停止服务...")
                service_process.terminate()
                print("✅ 服务已停止")
        else:
            print("❌ 配置文件打开失败")
            service_process.terminate()

    except Exception as e:
        print(f"❌ 启动失败: {e}")

def start_config_tool():
    """启动参数调整工具"""
    print("⚙️ 启动参数调整工具...")
    try:
        subprocess.run([sys.executable, "adjust_params.py"], check=True)
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="HohoAI 服务启动器")
    parser.add_argument("--mode", choices=["secure", "debug", "config"], help="启动模式")
    parser.add_argument("--skip-auth", action="store_true", help="跳过卡密验证（调试用）")
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.skip_auth:
        print("🔧 调试模式: 跳过卡密验证")
        start_debug_mode()
        return
    
    if args.mode:
        if args.mode == "secure":
            start_secure_mode()
        elif args.mode == "debug":
            start_debug_mode()
        elif args.mode == "config":
            start_config_tool()
        return
    
    # 交互式模式选择
    show_options()
    
    while True:
        try:
            choice = input("\n请选择启动模式 (1-3): ").strip()
            
            if choice == "1":
                start_secure_mode()
                break
            elif choice == "2":
                confirm = input("⚠️  确定要使用调试模式吗？(y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    start_debug_mode()
                    break
                else:
                    print("已取消调试模式")
            elif choice == "3":
                start_config_tool()
                break
            else:
                print("❌ 无效选择，请输入 1-3")
                
        except KeyboardInterrupt:
            print("\n👋 退出启动器")
            sys.exit(0)
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
